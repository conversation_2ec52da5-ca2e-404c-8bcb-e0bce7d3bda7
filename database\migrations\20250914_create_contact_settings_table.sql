-- Migration: Create contact_settings table for configurable contact information
-- Purpose: Store contact information for different driver and truck status types
-- Used by: DriverStatusErrorModal and Settings page

-- Create contact_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS contact_settings (
    id SERIAL PRIMARY KEY,
    category VARCHAR(20) NOT NULL CHECK (category IN ('driver', 'truck')),
    status_type VARCHAR(50) NOT NULL,
    primary_contact VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique combination of category and status_type
    UNIQUE(category, status_type)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_contact_settings_category_status 
ON contact_settings(category, status_type);

-- Insert default contact information for driver statuses
INSERT INTO contact_settings (category, status_type, primary_contact, phone, email, description)
VALUES 
    ('driver', 'inactive', 'Supervisor', '(*************', '<EMAIL>', 'Contact for inactive driver accounts'),
    ('driver', 'suspended', 'HR Department', '(*************', '<EMAIL>', 'Contact for suspended driver accounts'),
    ('driver', 'on_leave', 'Supervisor', '(*************', '<EMAIL>', 'Contact for drivers on leave'),
    ('driver', 'terminated', 'HR Department', '(*************', '<EMAIL>', 'Contact for terminated driver accounts')
ON CONFLICT (category, status_type) DO NOTHING;

-- Insert default contact information for truck statuses
INSERT INTO contact_settings (category, status_type, primary_contact, phone, email, description)
VALUES 
    ('truck', 'inactive', 'Fleet Manager', '(*************', '<EMAIL>', 'Contact for inactive trucks'),
    ('truck', 'maintenance', 'Maintenance Team', '(*************', '<EMAIL>', 'Contact for trucks under maintenance'),
    ('truck', 'retired', 'Fleet Manager', '(*************', '<EMAIL>', 'Contact for retired trucks')
ON CONFLICT (category, status_type) DO NOTHING;

-- Add comment to table
COMMENT ON TABLE contact_settings IS 'Configurable contact information for different driver and truck status types';
COMMENT ON COLUMN contact_settings.category IS 'Category: driver or truck';
COMMENT ON COLUMN contact_settings.status_type IS 'Status type within the category (e.g., inactive, suspended, maintenance)';
COMMENT ON COLUMN contact_settings.primary_contact IS 'Primary contact person or department name';
COMMENT ON COLUMN contact_settings.phone IS 'Contact phone number';
COMMENT ON COLUMN contact_settings.email IS 'Contact email address';
COMMENT ON COLUMN contact_settings.description IS 'Optional description of when to use this contact';
