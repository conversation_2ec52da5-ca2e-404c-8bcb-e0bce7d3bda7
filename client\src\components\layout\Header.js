import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { getApiBaseUrl } from '../../utils/network-utils';

const Header = ({ user, sidebarOpen, setSidebarOpen, sidebarHidden, setSidebarHidden }) => {
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [logoSettings, setLogoSettings] = useState(null);
  const { logout } = useAuth();
  const navigate = useNavigate();

  // Load logo settings from server
  useEffect(() => {
    const loadLogoSettings = async () => {
      try {
        const apiUrl = getApiBaseUrl();
        const response = await fetch(`${apiUrl}/upload/logos`);
        if (response.ok) {
          const result = await response.json();
          if (result.logos && result.logos.length > 0) {
            const latestLogo = result.logos[0];
            // Remove /api from apiUrl and add the logo path
            const baseUrl = apiUrl.replace('/api', '');
            setLogoSettings({
              src: `${baseUrl}${latestLogo.path}`,
              alt: 'Company Logo',
              width: 40,
              height: 40
            });
          } else {
            setLogoSettings(null);
          }
        }
      } catch (error) {
        console.error('Error loading logo settings:', error);
        setLogoSettings(null);
      }
    };

    loadLogoSettings();

    // Listen for custom logo update events
    const handleLogoUpdate = () => {
      loadLogoSettings();
    };

    window.addEventListener('logoUpdated', handleLogoUpdate);
    return () => window.removeEventListener('logoUpdated', handleLogoUpdate);
  }, []);

  const handleLogout = async () => {
    await logout();
  };

  const toggleUserMenu = () => {
    setUserMenuOpen(!userMenuOpen);
  };

  const handleSettingsClick = () => {
    setUserMenuOpen(false);
    navigate('/settings');
  };

  const handleHelpSupportClick = () => {
    setUserMenuOpen(false);
    navigate('/settings?tab=help-support');
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const toggleSidebarVisibility = () => {
    setSidebarHidden(!sidebarHidden);
    // Close mobile sidebar if it's open when hiding
    if (!sidebarHidden && sidebarOpen) {
      setSidebarOpen(false);
    }
  };

  return (
    <header className="bg-white shadow-sm border-b border-secondary-200 relative z-30">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">          {/* Mobile menu button */}
          <div className="flex items-center space-x-2">
            {/* Sidebar visibility toggle (desktop) */}
            <button
              onClick={toggleSidebarVisibility}
              className="hidden lg:block p-2 rounded-md text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              title={sidebarHidden ? "Show sidebar" : "Hide sidebar"}
            >
              <span className="sr-only">{sidebarHidden ? "Show" : "Hide"} sidebar</span>
              {sidebarHidden ? (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              ) : (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              )}
            </button>
            
            {/* Mobile menu button */}
            <button
              onClick={toggleSidebar}
              className="lg:hidden p-2 rounded-md text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <span className="sr-only">Open sidebar</span>
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>

          {/* Left side - Logo and title */}
          <div className="flex-1 lg:flex-none">
            <div className="flex items-center">
              {/* Logo */}
              <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center overflow-hidden bg-white rounded-md mr-3">
                {logoSettings?.src ? (
                  <img
                    src={logoSettings.src}
                    alt={logoSettings.alt}
                    className="object-contain max-w-full max-h-full"
                    style={{
                      maxWidth: '100%',
                      maxHeight: '100%'
                    }}
                    onError={(e) => {
                      e.target.style.display = 'none';
                      // Fallback to default emoji if logo fails to load
                    }}
                  />
                ) : (
                  <span className="text-2xl">🚛</span>
                )}
              </div>
              
              {/* Title with proper spacing and truncation */}
              <div className="min-w-0 flex-1">
                <h1 className="text-xl font-semibold text-secondary-800 font-custom-header truncate">
                  Hauling QR
                </h1>
              </div>
            </div>
          </div>

          {/* Right side - User menu and notifications */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <button className="p-2 rounded-full text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100 relative">
              <span className="sr-only">View notifications</span>
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7V5a4 4 0 00-8 0v2M7 13h10l4 8H3l4-8z" />
              </svg>
              {/* Notification badge */}
              <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-danger-500"></span>
            </button>

            {/* User menu dropdown */}
            <div className="relative">
              <button
                onClick={toggleUserMenu}
                className="flex items-center space-x-3 p-2 rounded-lg text-secondary-700 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <div className="flex items-center space-x-3">
                  {/* Avatar */}
                  <div className="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {user?.full_name?.charAt(0) || user?.username?.charAt(0) || 'A'}
                    </span>
                  </div>
                  
                  {/* User info */}
                  <div className="hidden md:block text-left">
                    <p className="text-sm font-medium text-secondary-900">
                      {user?.full_name || user?.username}
                    </p>
                    <p className="text-xs text-secondary-500 capitalize">
                      {user?.role}
                    </p>
                  </div>
                  
                  {/* Dropdown arrow */}
                  <svg className="h-4 w-4 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>

              {/* Dropdown menu */}
              {userMenuOpen && (
                <div className="absolute right-0 mt-2 w-56 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                  <div className="py-2">
                    {/* User info in dropdown */}
                    <div className="px-4 py-2 border-b border-secondary-200">
                      <p className="text-sm font-medium text-secondary-900">
                        {user?.full_name || user?.username}
                      </p>
                      <p className="text-sm text-secondary-500">
                        {user?.email}
                      </p>
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-100 text-primary-800 mt-1">
                        {user?.role}
                      </span>
                    </div>

                    {/* Menu items */}
                    <div className="py-1">
                      <button
                        onClick={handleSettingsClick}
                        className="flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100"
                      >
                        <svg className="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        Settings
                      </button>

                      <button
                        onClick={handleHelpSupportClick}
                        className="flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100"
                      >
                        <svg className="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Help & Support
                      </button>
                    </div>

                    {/* Logout */}
                    <div className="border-t border-secondary-200 py-1">
                      <button
                        onClick={handleLogout}
                        className="flex items-center w-full px-4 py-2 text-sm text-danger-600 hover:bg-danger-50"
                      >
                        <svg className="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        Sign Out
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Close dropdown when clicking outside */}
      {userMenuOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setUserMenuOpen(false)}
        />
      )}
    </header>
  );
};

export default Header;