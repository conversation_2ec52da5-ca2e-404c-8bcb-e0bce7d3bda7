# Hauling QR Trip Management System Deployment Configuration
# This file contains your deployment settings

# Domain Configuration
DOMAIN_NAME="truckhaul.top"          # CHANGE THIS: Your domain name
ENV_MODE="production"                # production or development
SSL_MODE="cloudflare"                # cloudflare or letsencrypt

# Admin Configuration
ADMIN_USERNAME="admin"               # Default admin username
ADMIN_PASSWORD="admin12345"          # CHANGE THIS: Strong password required
ADMIN_EMAIL="<EMAIL>"    # CHANGE THIS: Your email address

# Database Configuration
DB_HOST="localhost"                  # Database host
DB_PORT="5432"                      # Database port
DB_NAME="hauling_qr_system"         # Database name
DB_USER="hauling_app"               # Database user
DB_PASSWORD="PostgreSQLPassword123"  # CHANGE THIS: Strong database password

# GitHub Repository (with Personal Access Token)
REPO_URL="https://<EMAIL>/mightybadz18/hauling-qr-trip-management.git"
REPO_BRANCH="main"

# System Configuration
MONITORING_ENABLED=true              # Enable system monitoring
BACKUP_ENABLED=true                  # Enable automatic backups
BACKUP_RETENTION_DAYS=7              # Keep backups for 7 days

# Security Note: Keep this file secure and never commit it to version control
# Recommended: chmod 600 deployment-config.conf