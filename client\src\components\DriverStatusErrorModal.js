import React from 'react';
import toast from 'react-hot-toast';

/**
 * Driver and Truck Status Error Modal Component
 * Displays status-specific error messages with actionable guidance for blocked drivers and trucks
 */

// Driver status-specific styling and icons
const DRIVER_STATUS_STYLES = {
  inactive: {
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    textColor: 'text-red-800',
    iconColor: 'text-red-600',
    icon: '⚠️',
    title: 'Driver Account Inactive'
  },
  suspended: {
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    textColor: 'text-red-800',
    iconColor: 'text-red-600',
    icon: '🚫',
    title: 'Driver Account Suspended'
  },
  on_leave: {
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    textColor: 'text-orange-800',
    iconColor: 'text-orange-600',
    icon: '📅',
    title: 'Driver Currently On Leave'
  },
  terminated: {
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    textColor: 'text-red-800',
    iconColor: 'text-red-600',
    icon: '❌',
    title: 'Driver Employment Status Changed'
  }
};

// Truck status-specific styling and icons
const TRUCK_STATUS_STYLES = {
  inactive: {
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    textColor: 'text-red-800',
    iconColor: 'text-red-600',
    icon: '🚛',
    title: 'Truck Inactive'
  },
  maintenance: {
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    textColor: 'text-yellow-800',
    iconColor: 'text-yellow-600',
    icon: '🔧',
    title: 'Truck Under Maintenance'
  },
  retired: {
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    textColor: 'text-gray-800',
    iconColor: 'text-gray-600',
    icon: '🚫',
    title: 'Truck Retired'
  }
};

// Contact information for different status types
const DRIVER_CONTACT_INFO = {
  inactive: {
    primary: 'Supervisor',
    phone: '(*************',
    email: '<EMAIL>'
  },
  suspended: {
    primary: 'HR Department',
    phone: '(*************',
    email: '<EMAIL>'
  },
  on_leave: {
    primary: 'Supervisor',
    phone: '(*************',
    email: '<EMAIL>'
  },
  terminated: {
    primary: 'HR Department',
    phone: '(*************',
    email: '<EMAIL>'
  }
};

// Contact information for truck status issues
const TRUCK_CONTACT_INFO = {
  inactive: {
    primary: 'Fleet Manager',
    phone: '(*************',
    email: '<EMAIL>'
  },
  maintenance: {
    primary: 'Maintenance Team',
    phone: '(*************',
    email: '<EMAIL>'
  },
  retired: {
    primary: 'Fleet Manager',
    phone: '(*************',
    email: '<EMAIL>'
  }
};

const DriverStatusErrorModal = ({
  isOpen,
  onClose,
  driverStatus,
  truckStatus,
  statusDisplayName,
  message,
  driverInfo,
  truckInfo,
  entityType = 'driver', // 'driver' or 'truck'
  onRetry,
  // Enhanced sync context
  isSyncError = false,
  syncContext = null
}) => {
  if (!isOpen) return null;

  // Determine which entity and status to display
  const isDriverError = entityType === 'driver';
  const status = isDriverError ? driverStatus : truckStatus;
  const statusStyles = isDriverError ? DRIVER_STATUS_STYLES : TRUCK_STATUS_STYLES;
  const contactInfoMap = isDriverError ? DRIVER_CONTACT_INFO : TRUCK_CONTACT_INFO;

  const statusStyle = statusStyles[status] || (isDriverError ? DRIVER_STATUS_STYLES.inactive : TRUCK_STATUS_STYLES.inactive);
  const contactInfo = contactInfoMap[status] || (isDriverError ? DRIVER_CONTACT_INFO.inactive : TRUCK_CONTACT_INFO.inactive);

  const handleContactClick = (type, value) => {
    if (type === 'phone') {
      window.location.href = `tel:${value}`;
    } else if (type === 'email') {
      window.location.href = `mailto:${value}`;
    }
    toast.success(`Opening ${type} contact...`);
  };

  const handleRefreshStatus = () => {
    if (onRetry) {
      onRetry();
    }
    const entityName = isDriverError ? 'driver' : 'truck';
    toast.loading(`Refreshing ${entityName} status...`, { duration: 2000 });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 animate-fade-in">
        {/* Header */}
        <div className={`${statusStyle.bgColor} ${statusStyle.borderColor} border-b px-6 py-4 rounded-t-lg`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{statusStyle.icon}</span>
              <div>
                <h3 className={`text-lg font-semibold ${statusStyle.textColor}`}>
                  {isSyncError ? 'Sync Validation Error' : statusStyle.title}
                </h3>
                {isSyncError && syncContext && (
                  <p className={`text-sm ${statusStyle.textColor} opacity-80`}>
                    Connection {syncContext.currentIndex} of {syncContext.totalErrors}
                    {syncContext.totalErrors > 1 && ` • ${syncContext.totalErrors - syncContext.currentIndex} more after this`}
                  </p>
                )}
              </div>
            </div>
            <button
              onClick={onClose}
              className={`${statusStyle.textColor} hover:opacity-70 text-xl font-bold`}
            >
              ×
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-4">
          {/* Entity Information */}
          {isDriverError && driverInfo && (
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">Driver:</div>
              <div className="font-medium text-gray-900">
                {driverInfo.full_name} ({driverInfo.employee_id})
              </div>
              <div className="text-sm text-gray-500 mt-1">
                Status: <span className={`font-medium ${statusStyle.textColor}`}>
                  {statusDisplayName}
                </span>
              </div>
            </div>
          )}

          {!isDriverError && truckInfo && (
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600">Truck:</div>
              <div className="font-medium text-gray-900">
                {truckInfo.truck_number} ({truckInfo.license_plate})
              </div>
              {truckInfo.make && truckInfo.model && (
                <div className="text-sm text-gray-600">
                  {truckInfo.make} {truckInfo.model}
                </div>
              )}
              <div className="text-sm text-gray-500 mt-1">
                Status: <span className={`font-medium ${statusStyle.textColor}`}>
                  {statusDisplayName}
                </span>
              </div>
            </div>
          )}

          {/* Error Message */}
          <div className="mb-4">
            {isSyncError && (
              <div className="mb-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                <div className="flex items-center space-x-2 text-amber-800 text-sm font-medium mb-1">
                  <span>🔄</span>
                  <span>Sync Operation Blocked</span>
                </div>
                <p className="text-amber-700 text-sm">
                  This offline connection cannot be synced because the {entityType} status has changed since it was stored offline.
                </p>
              </div>
            )}
            <p className="text-gray-700 leading-relaxed">
              {message}
            </p>
          </div>

          {/* Contact Information */}
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="text-sm font-medium text-blue-800 mb-2">
              Contact {contactInfo.primary}:
            </div>
            <div className="space-y-2">
              <button
                onClick={() => handleContactClick('phone', contactInfo.phone)}
                className="flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors"
              >
                <span>📞</span>
                <span className="text-sm">{contactInfo.phone}</span>
              </button>
              <button
                onClick={() => handleContactClick('email', contactInfo.email)}
                className="flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors"
              >
                <span>✉️</span>
                <span className="text-sm">{contactInfo.email}</span>
              </button>
            </div>
          </div>

          {/* Additional Information */}
          <div className="text-xs text-gray-500 mb-4">
            {isDriverError ? (
              <>
                If you believe this is an error, please contact your supervisor immediately.
                Your account status may have been updated recently.
              </>
            ) : (
              <>
                If you believe this is an error, please contact the fleet manager or maintenance team.
                The truck status may have been updated recently.
              </>
            )}
          </div>
        </div>

        {/* Footer Actions */}
        <div className="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-between space-x-3">
          <button
            onClick={handleRefreshStatus}
            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            🔄 {isSyncError ? 'Refresh & Retry Sync' : 'Refresh Status'}
          </button>
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors text-sm font-medium"
          >
            {isSyncError && syncContext?.totalErrors > 1 ? 'Next Error' : 'Close'}
          </button>
        </div>
      </div>
    </div>
  );
};

// Status indicator component for displaying current driver status
export const DriverStatusIndicator = ({
  status,
  statusDisplayName,
  lastUpdated,
  isOffline = false,
  onRefresh
}) => {
  const getDriverStatusStyle = (status) => {
    switch (status) {
      case 'active':
        return {
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          borderColor: 'border-green-200',
          icon: '✓',
          label: 'Ready for work'
        };
      case 'inactive':
      case 'terminated':
        return {
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          borderColor: 'border-red-200',
          icon: '⚠',
          label: 'Contact supervisor'
        };
      case 'suspended':
        return {
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          borderColor: 'border-red-200',
          icon: '🚫',
          label: 'Contact HR'
        };
      case 'on_leave':
        return {
          bgColor: 'bg-orange-100',
          textColor: 'text-orange-800',
          borderColor: 'border-orange-200',
          icon: '📅',
          label: 'Currently unavailable'
        };
      case 'checked_in':
        return {
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-800',
          borderColor: 'border-blue-200',
          icon: '✅',
          label: 'Driver is checked in'
        };
      case 'checked_out':
        return {
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-200',
          icon: '◻',
          label: 'Driver is checked out'
        };
      default:
        return {
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-200',
          icon: '?',
          label: 'Unknown status'
        };
    }
  };

  const statusStyle = getDriverStatusStyle(status);

  return (
    <div className={`${statusStyle.bgColor} ${statusStyle.borderColor} border rounded-lg p-3 mb-4`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{statusStyle.icon}</span>
          <div>
            <div className={`font-medium ${statusStyle.textColor}`}>
              {statusDisplayName || status}
            </div>
            <div className="text-xs text-gray-600">
              {statusStyle.label}
            </div>
          </div>
        </div>
        
        {onRefresh && !isOffline && (
          <button
            onClick={onRefresh}
            className={`px-3 py-1 text-xs rounded ${statusStyle.textColor} hover:opacity-70 transition-opacity`}
          >
            🔄 Refresh
          </button>
        )}
      </div>
      
      {lastUpdated && (
        <div className="text-xs text-gray-500 mt-2">
          {isOffline ? 'Last updated: ' : 'Updated: '}
          {new Date(lastUpdated).toLocaleString()}
          {isOffline && ' (offline)'}
        </div>
      )}
    </div>
  );
};

// Status indicator component for displaying current truck status
export const TruckStatusIndicator = ({
  status,
  statusDisplayName,
  lastUpdated,
  isOffline = false,
  onRefresh
}) => {
  const getTruckStatusStyle = (status) => {
    switch (status) {
      case 'active':
        return {
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
          borderColor: 'border-green-200',
          icon: '🚛',
          label: 'Ready for service'
        };
      case 'inactive':
        return {
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
          borderColor: 'border-red-200',
          icon: '⚠',
          label: 'Contact fleet manager'
        };
      case 'maintenance':
        return {
          bgColor: 'bg-yellow-100',
          textColor: 'text-yellow-800',
          borderColor: 'border-yellow-200',
          icon: '🔧',
          label: 'Under maintenance'
        };
      case 'retired':
        return {
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-200',
          icon: '🚫',
          label: 'Out of service'
        };
      default:
        return {
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
          borderColor: 'border-gray-200',
          icon: '?',
          label: 'Unknown status'
        };
    }
  };

  const statusStyle = getTruckStatusStyle(status);

  return (
    <div className={`${statusStyle.bgColor} ${statusStyle.borderColor} border rounded-lg p-3 mb-4`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{statusStyle.icon}</span>
          <div>
            <div className={`font-medium ${statusStyle.textColor}`}>
              {statusDisplayName || status}
            </div>
            <div className="text-xs text-gray-600">
              {statusStyle.label}
            </div>
          </div>
        </div>

        {onRefresh && !isOffline && (
          <button
            onClick={onRefresh}
            className={`px-3 py-1 text-xs rounded ${statusStyle.textColor} hover:opacity-70 transition-opacity`}
          >
            🔄 Refresh
          </button>
        )}
      </div>

      {lastUpdated && (
        <div className="text-xs text-gray-500 mt-2">
          {isOffline ? 'Last updated: ' : 'Updated: '}
          {new Date(lastUpdated).toLocaleString()}
          {isOffline && ' (offline)'}
        </div>
      )}
    </div>
  );
};

export default DriverStatusErrorModal;
