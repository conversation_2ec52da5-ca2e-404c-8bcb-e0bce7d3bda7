--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

ALTER TABLE IF EXISTS ONLY public.trip_logs DROP CONSTRAINT IF EXISTS trip_logs_stopped_resolved_by_fkey;
ALTER TABLE IF EXISTS ONLY public.trip_logs DROP CONSTRAINT IF EXISTS trip_logs_performed_by_shift_id_fkey;
ALTER TABLE IF EXISTS ONLY public.trip_logs DROP CONSTRAINT IF EXISTS trip_logs_performed_by_driver_id_fkey;
ALTER TABLE IF EXISTS ONLY public.trip_logs DROP CONSTRAINT IF EXISTS trip_logs_exception_approved_by_fkey;
ALTER TABLE IF EXISTS ONLY public.trip_logs DROP CONSTRAINT IF EXISTS trip_logs_assignment_id_fkey;
ALTER TABLE IF EXISTS ONLY public.trip_logs DROP CONSTRAINT IF EXISTS trip_logs_actual_unloading_location_id_fkey;
ALTER TABLE IF EXISTS ONLY public.trip_logs DROP CONSTRAINT IF EXISTS trip_logs_actual_loading_location_id_fkey;
ALTER TABLE IF EXISTS ONLY public.system_tasks DROP CONSTRAINT IF EXISTS system_tasks_created_by_fkey;
ALTER TABLE IF EXISTS ONLY public.shift_handovers DROP CONSTRAINT IF EXISTS shift_handovers_truck_id_fkey;
ALTER TABLE IF EXISTS ONLY public.shift_handovers DROP CONSTRAINT IF EXISTS shift_handovers_outgoing_shift_id_fkey;
ALTER TABLE IF EXISTS ONLY public.shift_handovers DROP CONSTRAINT IF EXISTS shift_handovers_location_at_handover_fkey;
ALTER TABLE IF EXISTS ONLY public.shift_handovers DROP CONSTRAINT IF EXISTS shift_handovers_incoming_shift_id_fkey;
ALTER TABLE IF EXISTS ONLY public.shift_handovers DROP CONSTRAINT IF EXISTS shift_handovers_approved_by_fkey;
ALTER TABLE IF EXISTS ONLY public.shift_handovers DROP CONSTRAINT IF EXISTS shift_handovers_active_trip_id_fkey;
ALTER TABLE IF EXISTS ONLY public.scan_logs DROP CONSTRAINT IF EXISTS scan_logs_trip_log_id_fkey;
ALTER TABLE IF EXISTS ONLY public.scan_logs DROP CONSTRAINT IF EXISTS scan_logs_scanner_user_id_fkey;
ALTER TABLE IF EXISTS ONLY public.scan_logs DROP CONSTRAINT IF EXISTS scan_logs_scanned_truck_id_fkey;
ALTER TABLE IF EXISTS ONLY public.scan_logs DROP CONSTRAINT IF EXISTS scan_logs_scanned_location_id_fkey;
ALTER TABLE IF EXISTS ONLY public.driver_status_audit DROP CONSTRAINT IF EXISTS fk_driver_status_audit_driver;
ALTER TABLE IF EXISTS ONLY public.trip_logs DROP CONSTRAINT IF EXISTS fk_baseline_trip;
ALTER TABLE IF EXISTS ONLY public.driver_shifts DROP CONSTRAINT IF EXISTS driver_shifts_truck_id_fkey;
ALTER TABLE IF EXISTS ONLY public.driver_shifts DROP CONSTRAINT IF EXISTS driver_shifts_previous_shift_id_fkey;
ALTER TABLE IF EXISTS ONLY public.driver_shifts DROP CONSTRAINT IF EXISTS driver_shifts_driver_id_fkey;
ALTER TABLE IF EXISTS ONLY public.driver_shifts DROP CONSTRAINT IF EXISTS driver_shifts_assignment_id_fkey;
ALTER TABLE IF EXISTS ONLY public.assignments DROP CONSTRAINT IF EXISTS assignments_unloading_location_id_fkey;
ALTER TABLE IF EXISTS ONLY public.assignments DROP CONSTRAINT IF EXISTS assignments_truck_id_fkey;
ALTER TABLE IF EXISTS ONLY public.assignments DROP CONSTRAINT IF EXISTS assignments_shift_id_fkey;
ALTER TABLE IF EXISTS ONLY public.assignments DROP CONSTRAINT IF EXISTS assignments_shift_handover_id_fkey;
ALTER TABLE IF EXISTS ONLY public.assignments DROP CONSTRAINT IF EXISTS assignments_loading_location_id_fkey;
ALTER TABLE IF EXISTS ONLY public.assignments DROP CONSTRAINT IF EXISTS assignments_driver_id_fkey;
ALTER TABLE IF EXISTS ONLY public.approvals DROP CONSTRAINT IF EXISTS approvals_trip_log_id_fkey;
ALTER TABLE IF EXISTS ONLY public.approvals DROP CONSTRAINT IF EXISTS approvals_suggested_assignment_id_fkey;
ALTER TABLE IF EXISTS ONLY public.approvals DROP CONSTRAINT IF EXISTS approvals_reviewed_by_fkey;
ALTER TABLE IF EXISTS ONLY public.approvals DROP CONSTRAINT IF EXISTS approvals_reported_by_fkey;
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
DROP TRIGGER IF EXISTS update_trucks_updated_at ON public.dump_trucks;
DROP TRIGGER IF EXISTS update_trip_logs_updated_at ON public.trip_logs;
DROP TRIGGER IF EXISTS update_role_permissions_updated_at ON public.role_permissions;
DROP TRIGGER IF EXISTS update_locations_updated_at ON public.locations;
DROP TRIGGER IF EXISTS update_drivers_updated_at ON public.drivers;
DROP TRIGGER IF EXISTS update_assignments_updated_at ON public.assignments;
DROP TRIGGER IF EXISTS update_approvals_updated_at ON public.approvals;
DROP TRIGGER IF EXISTS trigger_update_assignment_on_trip_complete ON public.trip_logs;
DROP TRIGGER IF EXISTS trigger_sync_shift_date ON public.driver_shifts;
DROP TRIGGER IF EXISTS trigger_set_display_type ON public.driver_shifts;
DROP TRIGGER IF EXISTS trigger_calculate_trip_durations ON public.trip_logs;
DROP TRIGGER IF EXISTS trigger_auto_capture_trip_driver ON public.trip_logs;
DROP TRIGGER IF EXISTS trg_auto_populate_driver ON public.assignments;
DROP INDEX IF EXISTS public.idx_workflow_tracking;
DROP INDEX IF EXISTS public.idx_users_username;
DROP INDEX IF EXISTS public.idx_users_role;
DROP INDEX IF EXISTS public.idx_users_email;
DROP INDEX IF EXISTS public.idx_trucks_status;
DROP INDEX IF EXISTS public.idx_trucks_qr_data_gin;
DROP INDEX IF EXISTS public.idx_trucks_number;
DROP INDEX IF EXISTS public.idx_trucks_license;
DROP INDEX IF EXISTS public.idx_trucks_active_status;
DROP INDEX IF EXISTS public.idx_trips_time_analytics;
DROP INDEX IF EXISTS public.idx_trips_stopped_analytics;
DROP INDEX IF EXISTS public.idx_trips_status;
DROP INDEX IF EXISTS public.idx_trips_phase_durations;
DROP INDEX IF EXISTS public.idx_trips_location_performance;
DROP INDEX IF EXISTS public.idx_trips_exception_status;
DROP INDEX IF EXISTS public.idx_trips_exception;
DROP INDEX IF EXISTS public.idx_trips_duration_metrics;
DROP INDEX IF EXISTS public.idx_trips_date;
DROP INDEX IF EXISTS public.idx_trips_current_status_time;
DROP INDEX IF EXISTS public.idx_trips_assignment_status_exception;
DROP INDEX IF EXISTS public.idx_trips_assignment_status_date;
DROP INDEX IF EXISTS public.idx_trips_assignment;
DROP INDEX IF EXISTS public.idx_trips_actual_locations;
DROP INDEX IF EXISTS public.idx_trip_logs_stopped_status;
DROP INDEX IF EXISTS public.idx_trip_logs_stopped_resolved_at;
DROP INDEX IF EXISTS public.idx_trip_logs_stopped_reported_at;
DROP INDEX IF EXISTS public.idx_trip_logs_stopped_date;
DROP INDEX IF EXISTS public.idx_trip_logs_stopped;
DROP INDEX IF EXISTS public.idx_trip_logs_search_fields;
DROP INDEX IF EXISTS public.idx_trip_logs_performed_by_shift;
DROP INDEX IF EXISTS public.idx_trip_logs_performed_by_employee;
DROP INDEX IF EXISTS public.idx_trip_logs_performed_by_driver;
DROP INDEX IF EXISTS public.idx_trip_logs_notes_gin;
DROP INDEX IF EXISTS public.idx_trip_logs_duration_metrics;
DROP INDEX IF EXISTS public.idx_trip_logs_composite_filter;
DROP INDEX IF EXISTS public.idx_trip_logs_assignment_status_exception;
DROP INDEX IF EXISTS public.idx_trip_logs_assignment_status_created;
DROP INDEX IF EXISTS public.idx_trip_logs_actual_locations;
DROP INDEX IF EXISTS public.idx_system_tasks_type;
DROP INDEX IF EXISTS public.idx_system_tasks_status;
DROP INDEX IF EXISTS public.idx_system_tasks_scheduled_for;
DROP INDEX IF EXISTS public.idx_system_tasks_priority;
DROP INDEX IF EXISTS public.idx_system_tasks_created_at;
DROP INDEX IF EXISTS public.idx_system_tasks_auto_executable;
DROP INDEX IF EXISTS public.idx_system_logs_user_id;
DROP INDEX IF EXISTS public.idx_system_logs_log_type;
DROP INDEX IF EXISTS public.idx_system_logs_created_at;
DROP INDEX IF EXISTS public.idx_system_health_logs_status;
DROP INDEX IF EXISTS public.idx_system_health_logs_module;
DROP INDEX IF EXISTS public.idx_system_health_logs_checked_at;
DROP INDEX IF EXISTS public.idx_shift_handovers_truck;
DROP INDEX IF EXISTS public.idx_shift_handovers_trip;
DROP INDEX IF EXISTS public.idx_shift_handovers_time;
DROP INDEX IF EXISTS public.idx_security_logs_risk_level;
DROP INDEX IF EXISTS public.idx_security_logs_ip_address;
DROP INDEX IF EXISTS public.idx_security_logs_details_gin;
DROP INDEX IF EXISTS public.idx_security_logs_created_at;
DROP INDEX IF EXISTS public.idx_security_logs_activity_type;
DROP INDEX IF EXISTS public.idx_scans_user_valid_timestamp;
DROP INDEX IF EXISTS public.idx_scans_type;
DROP INDEX IF EXISTS public.idx_scans_trip_type;
DROP INDEX IF EXISTS public.idx_scans_trip;
DROP INDEX IF EXISTS public.idx_scans_timestamp;
DROP INDEX IF EXISTS public.idx_scans_location;
DROP INDEX IF EXISTS public.idx_scan_logs_timestamp_user;
DROP INDEX IF EXISTS public.idx_role_permissions_role_page;
DROP INDEX IF EXISTS public.idx_role_permissions_role;
DROP INDEX IF EXISTS public.idx_mv_trip_performance_unique;
DROP INDEX IF EXISTS public.idx_mv_fleet_status_truck_id;
DROP INDEX IF EXISTS public.idx_mv_fleet_status_trip_status;
DROP INDEX IF EXISTS public.idx_locations_type;
DROP INDEX IF EXISTS public.idx_locations_qr_data_gin;
DROP INDEX IF EXISTS public.idx_locations_is_active;
DROP INDEX IF EXISTS public.idx_locations_id_name;
DROP INDEX IF EXISTS public.idx_locations_code;
DROP INDEX IF EXISTS public.idx_locations_active_type;
DROP INDEX IF EXISTS public.idx_locations_active;
DROP INDEX IF EXISTS public.idx_location_sequence;
DROP INDEX IF EXISTS public.idx_health_check_logs_status;
DROP INDEX IF EXISTS public.idx_health_check_logs_checked_at;
DROP INDEX IF EXISTS public.idx_health_check_logs_check_name;
DROP INDEX IF EXISTS public.idx_drivers_status;
DROP INDEX IF EXISTS public.idx_drivers_qr_code_gin;
DROP INDEX IF EXISTS public.idx_drivers_license;
DROP INDEX IF EXISTS public.idx_drivers_employee_id;
DROP INDEX IF EXISTS public.idx_drivers_active_status;
DROP INDEX IF EXISTS public.idx_driver_status_audit_status_operation;
DROP INDEX IF EXISTS public.idx_driver_status_audit_monitoring;
DROP INDEX IF EXISTS public.idx_driver_status_audit_employee_id;
DROP INDEX IF EXISTS public.idx_driver_status_audit_driver_id;
DROP INDEX IF EXISTS public.idx_driver_status_audit_blocked_at;
DROP INDEX IF EXISTS public.idx_driver_shifts_unified_range;
DROP INDEX IF EXISTS public.idx_driver_shifts_unified_date_range;
DROP INDEX IF EXISTS public.idx_driver_shifts_truck_status_date_time;
DROP INDEX IF EXISTS public.idx_driver_shifts_truck_status_active;
DROP INDEX IF EXISTS public.idx_driver_shifts_truck_status;
DROP INDEX IF EXISTS public.idx_driver_shifts_truck_shift_date;
DROP INDEX IF EXISTS public.idx_driver_shifts_time_range;
DROP INDEX IF EXISTS public.idx_driver_shifts_status_date;
DROP INDEX IF EXISTS public.idx_driver_shifts_status;
DROP INDEX IF EXISTS public.idx_driver_shifts_start_date_status;
DROP INDEX IF EXISTS public.idx_driver_shifts_start_date;
DROP INDEX IF EXISTS public.idx_driver_shifts_shift_date;
DROP INDEX IF EXISTS public.idx_driver_shifts_security_context_gin;
DROP INDEX IF EXISTS public.idx_driver_shifts_recurrence_status;
DROP INDEX IF EXISTS public.idx_driver_shifts_recurrence_active;
DROP INDEX IF EXISTS public.idx_driver_shifts_end_date;
DROP INDEX IF EXISTS public.idx_driver_shifts_driver_truck_status;
DROP INDEX IF EXISTS public.idx_driver_shifts_driver_status;
DROP INDEX IF EXISTS public.idx_driver_shifts_datetime_combo;
DROP INDEX IF EXISTS public.idx_driver_shifts_date_range_status;
DROP INDEX IF EXISTS public.idx_driver_shifts_date_range;
DROP INDEX IF EXISTS public.idx_driver_shifts_active_truck_date;
DROP INDEX IF EXISTS public.idx_baseline_trip;
DROP INDEX IF EXISTS public.idx_automated_fix_logs_success;
DROP INDEX IF EXISTS public.idx_automated_fix_logs_module_name;
DROP INDEX IF EXISTS public.idx_automated_fix_logs_executed_at;
DROP INDEX IF EXISTS public.idx_assignments_truck_locations;
DROP INDEX IF EXISTS public.idx_assignments_truck_id_status;
DROP INDEX IF EXISTS public.idx_assignments_truck_driver_status;
DROP INDEX IF EXISTS public.idx_assignments_truck_driver_date;
DROP INDEX IF EXISTS public.idx_assignments_truck_driver;
DROP INDEX IF EXISTS public.idx_assignments_truck;
DROP INDEX IF EXISTS public.idx_assignments_status_date;
DROP INDEX IF EXISTS public.idx_assignments_status_assigned_date;
DROP INDEX IF EXISTS public.idx_assignments_status;
DROP INDEX IF EXISTS public.idx_assignments_shift_truck;
DROP INDEX IF EXISTS public.idx_assignments_shift;
DROP INDEX IF EXISTS public.idx_assignments_priority;
DROP INDEX IF EXISTS public.idx_assignments_locations_date;
DROP INDEX IF EXISTS public.idx_assignments_exact_duplicate;
DROP INDEX IF EXISTS public.idx_assignments_driver;
DROP INDEX IF EXISTS public.idx_assignments_date;
DROP INDEX IF EXISTS public.idx_assignments_auto_created;
DROP INDEX IF EXISTS public.idx_assignments_assignment_code;
DROP INDEX IF EXISTS public.idx_assignments_assigned_date_status;
DROP INDEX IF EXISTS public.idx_assignments_adaptive_status;
DROP INDEX IF EXISTS public.idx_assignments_adaptive;
DROP INDEX IF EXISTS public.idx_assignments_adaptation_strategy;
DROP INDEX IF EXISTS public.idx_assignments_adaptation_metadata_gin;
DROP INDEX IF EXISTS public.idx_assignments_adaptation_confidence;
DROP INDEX IF EXISTS public.idx_assignments_active_operations;
DROP INDEX IF EXISTS public.idx_approvals_trip_status_created;
DROP INDEX IF EXISTS public.idx_approvals_trip;
DROP INDEX IF EXISTS public.idx_approvals_suggested_assignment;
DROP INDEX IF EXISTS public.idx_approvals_status;
DROP INDEX IF EXISTS public.idx_approvals_severity_created;
DROP INDEX IF EXISTS public.idx_approvals_severity;
DROP INDEX IF EXISTS public.idx_approvals_requested_at;
DROP INDEX IF EXISTS public.idx_approvals_reported_by;
DROP INDEX IF EXISTS public.idx_approvals_auto_approved;
DROP INDEX IF EXISTS public.idx_approvals_adaptive_metadata_gin;
DROP INDEX IF EXISTS public.idx_approvals_adaptive;
DROP INDEX IF EXISTS public.idx_approvals_adaptation_strategy;
DROP INDEX IF EXISTS public.idx_approvals_adaptation_confidence;
ALTER TABLE IF EXISTS ONLY public.users DROP CONSTRAINT IF EXISTS users_username_key;
ALTER TABLE IF EXISTS ONLY public.users DROP CONSTRAINT IF EXISTS users_pkey;
ALTER TABLE IF EXISTS ONLY public.users DROP CONSTRAINT IF EXISTS users_email_key;
ALTER TABLE IF EXISTS ONLY public.driver_shifts DROP CONSTRAINT IF EXISTS unique_active_driver_shift;
ALTER TABLE IF EXISTS ONLY public.trip_logs DROP CONSTRAINT IF EXISTS trip_logs_pkey;
ALTER TABLE IF EXISTS ONLY public.trip_logs DROP CONSTRAINT IF EXISTS trip_logs_assignment_id_trip_number_key;
ALTER TABLE IF EXISTS ONLY public.system_tasks DROP CONSTRAINT IF EXISTS system_tasks_pkey;
ALTER TABLE IF EXISTS ONLY public.system_logs DROP CONSTRAINT IF EXISTS system_logs_pkey;
ALTER TABLE IF EXISTS ONLY public.system_health_logs DROP CONSTRAINT IF EXISTS system_health_logs_pkey;
ALTER TABLE IF EXISTS ONLY public.shift_handovers DROP CONSTRAINT IF EXISTS shift_handovers_pkey;
ALTER TABLE IF EXISTS ONLY public.security_logs DROP CONSTRAINT IF EXISTS security_logs_pkey;
ALTER TABLE IF EXISTS ONLY public.scan_logs DROP CONSTRAINT IF EXISTS scan_logs_pkey;
ALTER TABLE IF EXISTS ONLY public.role_permissions DROP CONSTRAINT IF EXISTS role_permissions_role_name_page_key_key;
ALTER TABLE IF EXISTS ONLY public.role_permissions DROP CONSTRAINT IF EXISTS role_permissions_pkey;
ALTER TABLE IF EXISTS ONLY public.migrations DROP CONSTRAINT IF EXISTS migrations_pkey;
ALTER TABLE IF EXISTS ONLY public.migrations DROP CONSTRAINT IF EXISTS migrations_filename_key;
ALTER TABLE IF EXISTS ONLY public.migration_log DROP CONSTRAINT IF EXISTS migration_log_pkey;
ALTER TABLE IF EXISTS ONLY public.migration_log DROP CONSTRAINT IF EXISTS migration_log_migration_name_key;
ALTER TABLE IF EXISTS ONLY public.locations DROP CONSTRAINT IF EXISTS locations_pkey;
ALTER TABLE IF EXISTS ONLY public.locations DROP CONSTRAINT IF EXISTS locations_location_code_key;
ALTER TABLE IF EXISTS ONLY public.health_check_logs DROP CONSTRAINT IF EXISTS health_check_logs_pkey;
ALTER TABLE IF EXISTS ONLY public.dump_trucks DROP CONSTRAINT IF EXISTS dump_trucks_truck_number_key;
ALTER TABLE IF EXISTS ONLY public.dump_trucks DROP CONSTRAINT IF EXISTS dump_trucks_pkey;
ALTER TABLE IF EXISTS ONLY public.dump_trucks DROP CONSTRAINT IF EXISTS dump_trucks_license_plate_key;
ALTER TABLE IF EXISTS ONLY public.drivers DROP CONSTRAINT IF EXISTS drivers_pkey;
ALTER TABLE IF EXISTS ONLY public.drivers DROP CONSTRAINT IF EXISTS drivers_license_number_key;
ALTER TABLE IF EXISTS ONLY public.drivers DROP CONSTRAINT IF EXISTS drivers_employee_id_key;
ALTER TABLE IF EXISTS ONLY public.driver_status_audit DROP CONSTRAINT IF EXISTS driver_status_audit_pkey;
ALTER TABLE IF EXISTS ONLY public.driver_shifts DROP CONSTRAINT IF EXISTS driver_shifts_pkey;
ALTER TABLE IF EXISTS ONLY public.automated_fix_logs DROP CONSTRAINT IF EXISTS automated_fix_logs_pkey;
ALTER TABLE IF EXISTS ONLY public.assignments DROP CONSTRAINT IF EXISTS assignments_pkey;
ALTER TABLE IF EXISTS ONLY public.assignments DROP CONSTRAINT IF EXISTS assignments_assignment_code_key;
ALTER TABLE IF EXISTS ONLY public.approvals DROP CONSTRAINT IF EXISTS approvals_pkey;
ALTER TABLE IF EXISTS public.users ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.trip_logs ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.system_tasks ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.system_logs ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.system_health_logs ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.shift_handovers ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.security_logs ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.scan_logs ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.role_permissions ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.migrations ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.migration_log ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.locations ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.health_check_logs ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.dump_trucks ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.drivers ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.driver_status_audit ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.driver_shifts ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.automated_fix_logs ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.assignments ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.approvals ALTER COLUMN id DROP DEFAULT;
DROP VIEW IF EXISTS public.v_workflow_analytics;
DROP VIEW IF EXISTS public.v_trip_summary;
DROP VIEW IF EXISTS public.v_trip_performance;
DROP VIEW IF EXISTS public.v_realtime_dashboard;
DROP VIEW IF EXISTS public.v_dynamic_assignment_analytics;
DROP VIEW IF EXISTS public.v_active_exceptions;
DROP SEQUENCE IF EXISTS public.users_id_seq;
DROP TABLE IF EXISTS public.users;
DROP SEQUENCE IF EXISTS public.trip_logs_id_seq;
DROP SEQUENCE IF EXISTS public.system_tasks_id_seq;
DROP TABLE IF EXISTS public.system_tasks;
DROP SEQUENCE IF EXISTS public.system_logs_id_seq;
DROP TABLE IF EXISTS public.system_logs;
DROP SEQUENCE IF EXISTS public.system_health_logs_id_seq;
DROP TABLE IF EXISTS public.system_health_logs;
DROP SEQUENCE IF EXISTS public.shift_handovers_id_seq;
DROP TABLE IF EXISTS public.shift_handovers;
DROP SEQUENCE IF EXISTS public.security_logs_id_seq;
DROP VIEW IF EXISTS public.security_dashboard;
DROP TABLE IF EXISTS public.security_logs;
DROP SEQUENCE IF EXISTS public.scan_logs_id_seq;
DROP TABLE IF EXISTS public.scan_logs;
DROP SEQUENCE IF EXISTS public.role_permissions_id_seq;
DROP TABLE IF EXISTS public.role_permissions;
DROP MATERIALIZED VIEW IF EXISTS public.mv_trip_performance_summary;
DROP MATERIALIZED VIEW IF EXISTS public.mv_stopped_analytics_summary;
DROP MATERIALIZED VIEW IF EXISTS public.mv_fleet_status_summary;
DROP MATERIALIZED VIEW IF EXISTS public.mv_fleet_performance_summary;
DROP TABLE IF EXISTS public.trip_logs;
DROP SEQUENCE IF EXISTS public.migrations_id_seq;
DROP TABLE IF EXISTS public.migrations;
DROP SEQUENCE IF EXISTS public.migration_log_id_seq;
DROP TABLE IF EXISTS public.migration_log;
DROP SEQUENCE IF EXISTS public.locations_id_seq;
DROP TABLE IF EXISTS public.locations;
DROP SEQUENCE IF EXISTS public.health_check_logs_id_seq;
DROP TABLE IF EXISTS public.health_check_logs;
DROP SEQUENCE IF EXISTS public.dump_trucks_id_seq;
DROP TABLE IF EXISTS public.dump_trucks;
DROP SEQUENCE IF EXISTS public.drivers_id_seq;
DROP VIEW IF EXISTS public.driver_status_audit_summary;
DROP TABLE IF EXISTS public.drivers;
DROP SEQUENCE IF EXISTS public.driver_status_audit_id_seq;
DROP TABLE IF EXISTS public.driver_status_audit;
DROP SEQUENCE IF EXISTS public.driver_shifts_id_seq;
DROP TABLE IF EXISTS public.driver_shifts;
DROP SEQUENCE IF EXISTS public.automated_fix_logs_id_seq;
DROP TABLE IF EXISTS public.automated_fix_logs;
DROP SEQUENCE IF EXISTS public.assignments_id_seq;
DROP TABLE IF EXISTS public.assignments;
DROP SEQUENCE IF EXISTS public.approvals_id_seq;
DROP TABLE IF EXISTS public.approvals;
DROP FUNCTION IF EXISTS public.validate_driver_qr_code(qr_data jsonb);
DROP FUNCTION IF EXISTS public.validate_all_shift_statuses();
DROP FUNCTION IF EXISTS public.update_updated_at_column();
DROP FUNCTION IF EXISTS public.update_shift_status(p_shift_id integer, p_reference_timestamp timestamp without time zone);
DROP FUNCTION IF EXISTS public.update_assignment_on_trip_complete();
DROP FUNCTION IF EXISTS public.update_all_shift_statuses(p_reference_timestamp timestamp with time zone);
DROP FUNCTION IF EXISTS public.test_shift_time_logic(p_start_time time without time zone, p_end_time time without time zone, p_test_time time without time zone, p_is_overnight boolean);
DROP FUNCTION IF EXISTS public.test_shift_scenarios();
DROP FUNCTION IF EXISTS public.sync_shift_date_with_start_date();
DROP FUNCTION IF EXISTS public.shift_system_health_check();
DROP FUNCTION IF EXISTS public.shift_overlaps_range(p_start_date date, p_end_date date, p_range_start date, p_range_end date);
DROP FUNCTION IF EXISTS public.shift_includes_date(p_start_date date, p_end_date date, p_query_date date);
DROP FUNCTION IF EXISTS public.set_display_type_trigger();
DROP FUNCTION IF EXISTS public.schedule_shift_activation();
DROP FUNCTION IF EXISTS public.schedule_auto_activation();
DROP FUNCTION IF EXISTS public.refresh_trip_performance_summary();
DROP FUNCTION IF EXISTS public.refresh_fleet_status_summary();
DROP FUNCTION IF EXISTS public.refresh_breakdown_analytics_summary();
DROP FUNCTION IF EXISTS public.refresh_all_analytics_views();
DROP FUNCTION IF EXISTS public.monitor_shift_system();
DROP FUNCTION IF EXISTS public.log_system_event(p_log_type character varying, p_message text, p_details jsonb, p_user_id integer);
DROP FUNCTION IF EXISTS public.log_automated_fix(p_module_name character varying, p_fix_type character varying, p_success boolean, p_message text, p_details jsonb, p_affected_records integer);
DROP FUNCTION IF EXISTS public.is_trip_terminal(p_status public.trip_status);
DROP FUNCTION IF EXISTS public.is_shift_active_on_date(p_shift_id integer, p_check_date date);
DROP FUNCTION IF EXISTS public.is_overnight_shift(start_time time without time zone, end_time time without time zone);
DROP FUNCTION IF EXISTS public.handover_driver_shift(p_truck_id integer, p_new_driver_id integer, p_handover_notes text);
DROP FUNCTION IF EXISTS public.get_user_roles();
DROP FUNCTION IF EXISTS public.get_shift_status_summary(p_reference_timestamp timestamp without time zone);
DROP FUNCTION IF EXISTS public.get_shift_display_date(p_shift_date date, p_start_date date, p_end_date date, p_recurrence_pattern text);
DROP FUNCTION IF EXISTS public.get_exception_analytics(p_days integer);
DROP FUNCTION IF EXISTS public.get_database_performance_metrics();
DROP FUNCTION IF EXISTS public.get_current_driver_for_truck_enhanced(p_truck_id integer, p_check_date date, p_check_time time without time zone);
DROP FUNCTION IF EXISTS public.get_current_driver_for_truck(p_truck_id integer);
DROP FUNCTION IF EXISTS public.get_current_active_driver(p_truck_id integer);
DROP FUNCTION IF EXISTS public.get_advanced_exception_analytics(p_start_date date, p_end_date date);
DROP FUNCTION IF EXISTS public.get_active_shifts_for_date(p_check_date date);
DROP FUNCTION IF EXISTS public.get_active_driver_shift_for_truck(p_truck_id integer);
DROP FUNCTION IF EXISTS public.fix_incorrectly_completed_shifts();
DROP FUNCTION IF EXISTS public.evaluate_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone);
DROP FUNCTION IF EXISTS public.evaluate_shift_status(p_shift_id integer);
DROP FUNCTION IF EXISTS public.determine_shift_type_by_time(p_start_time time without time zone);
DROP FUNCTION IF EXISTS public.debug_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone);
DROP FUNCTION IF EXISTS public.create_unified_shift(p_truck_id integer, p_driver_id integer, p_shift_type public.shift_type, p_start_date date, p_end_date date, p_start_time time without time zone, p_end_time time without time zone, p_status public.shift_status, p_handover_notes text);
DROP FUNCTION IF EXISTS public.create_shift_assignment(p_truck_id integer, p_shift_id integer, p_loading_location_id integer, p_unloading_location_id integer);
DROP FUNCTION IF EXISTS public.create_deviation_assignment(p_truck_id integer, p_driver_id integer, p_loading_location_id integer, p_unloading_location_id integer, p_priority character varying, p_expected_loads integer);
DROP FUNCTION IF EXISTS public.create_assignment_with_auto_driver(p_assignment_code character varying, p_truck_id integer, p_loading_location_id integer, p_unloading_location_id integer, p_assigned_date date, p_priority character varying, p_expected_loads integer, p_notes text);
DROP FUNCTION IF EXISTS public.complete_shift_manually(p_shift_id integer, p_completed_by integer);
DROP FUNCTION IF EXISTS public.cleanup_shift_system();
DROP FUNCTION IF EXISTS public.cleanup_security_logs();
DROP FUNCTION IF EXISTS public.cleanup_driver_status_audit(retention_days integer);
DROP FUNCTION IF EXISTS public.classify_shift_by_time(p_start_time time without time zone, p_end_time time without time zone);
DROP FUNCTION IF EXISTS public.check_shift_status_consistency();
DROP FUNCTION IF EXISTS public.capture_active_driver_for_trip_enhanced_test(p_truck_id integer, p_timestamp timestamp without time zone);
DROP FUNCTION IF EXISTS public.capture_active_driver_for_trip_enhanced(p_truck_id integer, p_timestamp timestamp without time zone);
DROP FUNCTION IF EXISTS public.capture_active_driver_for_trip(p_truck_id integer, p_timestamp timestamp without time zone);
DROP FUNCTION IF EXISTS public.can_delete_user_role(role_to_check text);
DROP FUNCTION IF EXISTS public.calculate_trip_durations();
DROP FUNCTION IF EXISTS public.calculate_shift_status(within_date_range boolean, within_time_window boolean);
DROP FUNCTION IF EXISTS public.calculate_shift_end_timestamp(end_date date, end_time time without time zone, is_overnight boolean);
DROP FUNCTION IF EXISTS public.auto_populate_driver_from_shift();
DROP FUNCTION IF EXISTS public.auto_complete_shifts_enhanced();
DROP FUNCTION IF EXISTS public.auto_capture_trip_driver();
DROP FUNCTION IF EXISTS public.auto_activate_shifts_enhanced();
DROP FUNCTION IF EXISTS public.auto_activate_shifts();
DROP FUNCTION IF EXISTS public.analyze_security_incidents(p_start_date date, p_end_date date);
DROP FUNCTION IF EXISTS public.add_user_role_enum(new_role text);
DROP FUNCTION IF EXISTS public.add_sample_shift_data();
DROP TYPE IF EXISTS public.user_role;
DROP TYPE IF EXISTS public.truck_status;
DROP TYPE IF EXISTS public.trip_status;
DROP TYPE IF EXISTS public.shift_type;
DROP TYPE IF EXISTS public.shift_status;
DROP TYPE IF EXISTS public.scan_type;
DROP TYPE IF EXISTS public.recurrence_pattern;
DROP TYPE IF EXISTS public.location_type;
DROP TYPE IF EXISTS public.driver_status;
DROP TYPE IF EXISTS public.assignment_status;
DROP TYPE IF EXISTS public.approval_status;
DROP EXTENSION IF EXISTS pg_trgm;
--
-- Name: pg_trgm; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_trgm WITH SCHEMA public;


--
-- Name: EXTENSION pg_trgm; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_trgm IS 'text similarity measurement and index searching based on trigrams';


--
-- Name: approval_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.approval_status AS ENUM (
    'pending',
    'approved',
    'rejected'
);


--
-- Name: assignment_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.assignment_status AS ENUM (
    'pending_approval',
    'assigned',
    'in_progress',
    'completed',
    'cancelled'
);


--
-- Name: driver_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.driver_status AS ENUM (
    'active',
    'inactive',
    'on_leave',
    'terminated',
    'suspended'
);


--
-- Name: location_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.location_type AS ENUM (
    'loading',
    'unloading',
    'checkpoint'
);


--
-- Name: recurrence_pattern; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.recurrence_pattern AS ENUM (
    'single',
    'daily',
    'weekly',
    'weekdays',
    'weekends',
    'custom'
);


--
-- Name: scan_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.scan_type AS ENUM (
    'location_scan',
    'truck_scan',
    'loading_start',
    'loading_end',
    'unloading_start',
    'unloading_end'
);


--
-- Name: shift_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.shift_status AS ENUM (
    'scheduled',
    'active',
    'completed',
    'cancelled'
);


--
-- Name: shift_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.shift_type AS ENUM (
    'day',
    'night',
    'custom'
);


--
-- Name: trip_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.trip_status AS ENUM (
    'assigned',
    'loading_start',
    'loading_end',
    'unloading_start',
    'unloading_end',
    'trip_completed',
    'exception_pending',
    'cancelled',
    'stopped',
    'exception_triggered'
);


--
-- Name: truck_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.truck_status AS ENUM (
    'active',
    'inactive',
    'maintenance',
    'retired'
);


--
-- Name: user_role; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.user_role AS ENUM (
    'admin',
    'supervisor',
    'operator',
    'dispatcher'
);


--
-- Name: add_sample_shift_data(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.add_sample_shift_data() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_inserted_count INTEGER := 0;
BEGIN
    -- Only add sample data if no shifts exist
    IF NOT EXISTS (SELECT 1 FROM driver_shifts LIMIT 1) THEN
        -- Insert sample shifts for existing trucks (if they exist)
        INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
        SELECT
            dt.id as truck_id,
            d.id as driver_id,
            'day' as shift_type,
            CURRENT_DATE as shift_date,
            '06:00:00'::TIME as start_time,
            '18:00:00'::TIME as end_time,
            'scheduled' as status
        FROM dump_trucks dt
        CROSS JOIN drivers d
        WHERE dt.status = 'active'
          AND d.status = 'active'
        LIMIT 3; -- Limit to prevent too much sample data
        
        GET DIAGNOSTICS v_inserted_count = ROW_COUNT;
        
        -- Log the sample data creation
        PERFORM log_system_event(
            'SAMPLE_DATA_CREATION',
            'Sample shift data created for testing',
            jsonb_build_object(
                'shifts_created', v_inserted_count,
                'creation_time', CURRENT_TIMESTAMP
            )
        );
    END IF;
    
    RETURN v_inserted_count;
END;
$$;


--
-- Name: FUNCTION add_sample_shift_data(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.add_sample_shift_data() IS 'Add sample shift data for testing purposes (only if no shifts exist)';


--
-- Name: add_user_role_enum(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.add_user_role_enum(new_role text) RETURNS void
    LANGUAGE plpgsql
    AS $$

BEGIN

    -- Check if role already exists

    IF NOT EXISTS (

        SELECT 1 FROM pg_enum 

        WHERE enumlabel = new_role 

        AND enumtypid = 'user_role'::regtype

    ) THEN

        EXECUTE format('ALTER TYPE user_role ADD VALUE %L', new_role);

    END IF;

END;

$$;


--
-- Name: FUNCTION add_user_role_enum(new_role text); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.add_user_role_enum(new_role text) IS 'Safely adds a new value to the user_role enum if it does not already exist';


--
-- Name: analyze_security_incidents(date, date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.analyze_security_incidents(p_start_date date DEFAULT (CURRENT_DATE - '7 days'::interval), p_end_date date DEFAULT CURRENT_DATE) RETURNS TABLE(activity_type character varying, incident_count bigint, unique_ips bigint, high_risk_count bigint, top_ip inet, top_ip_count bigint)
    LANGUAGE plpgsql
    AS $$

BEGIN

    RETURN QUERY

    SELECT 

        sl.activity_type,

        COUNT(*) as incident_count,

        COUNT(DISTINCT sl.ip_address) as unique_ips,

        COUNT(*) FILTER (WHERE sl.risk_level IN ('HIGH', 'CRITICAL')) as high_risk_count,

        (

            SELECT ip_address 

            FROM security_logs sl2 

            WHERE sl2.activity_type = sl.activity_type 

              AND sl2.created_at BETWEEN p_start_date AND p_end_date + INTERVAL '1 day'

            GROUP BY ip_address 

            ORDER BY COUNT(*) DESC 

            LIMIT 1

        ) as top_ip,

        (

            SELECT COUNT(*) 

            FROM security_logs sl3 

            WHERE sl3.activity_type = sl.activity_type 

              AND sl3.ip_address = (

                  SELECT ip_address 

                  FROM security_logs sl4 

                  WHERE sl4.activity_type = sl.activity_type 

                    AND sl4.created_at BETWEEN p_start_date AND p_end_date + INTERVAL '1 day'

                  GROUP BY ip_address 

                  ORDER BY COUNT(*) DESC 

                  LIMIT 1

              )

              AND sl3.created_at BETWEEN p_start_date AND p_end_date + INTERVAL '1 day'

        ) as top_ip_count

    FROM security_logs sl

    WHERE sl.created_at BETWEEN p_start_date AND p_end_date + INTERVAL '1 day'

    GROUP BY sl.activity_type

    ORDER BY incident_count DESC;

END;

$$;


--
-- Name: FUNCTION analyze_security_incidents(p_start_date date, p_end_date date); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.analyze_security_incidents(p_start_date date, p_end_date date) IS 'Security incident analysis for specified date range';


--
-- Name: auto_activate_shifts(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.auto_activate_shifts() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Auto-activate shifts that should be starting now
    UPDATE driver_shifts 
    SET status = 'active', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'scheduled'
        AND shift_date = CURRENT_DATE
        AND CURRENT_TIME >= start_time
        AND CURRENT_TIME < CASE 
            WHEN end_time < start_time 
            THEN end_time + interval '24 hours'
            ELSE end_time 
        END;
    
    RETURN NULL;
END;
$$;


--
-- Name: auto_activate_shifts_enhanced(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.auto_activate_shifts_enhanced() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_activated_count INTEGER := 0;
    v_shift_record RECORD;
BEGIN
    -- Get all shifts that should be activated now
    FOR v_shift_record IN
        SELECT ds.id, ds.truck_id
        FROM driver_shifts ds
        WHERE ds.status = 'scheduled'
          AND is_shift_active_on_date(ds.id, CURRENT_DATE)
          AND CURRENT_TIME >= ds.start_time
          AND CURRENT_TIME < CASE 
              WHEN ds.end_time < ds.start_time 
              THEN ds.end_time + interval '24 hours'
              ELSE ds.end_time 
          END
    LOOP
        -- Deactivate any currently active shifts for this truck
        UPDATE driver_shifts 
        SET status = 'completed', updated_at = CURRENT_TIMESTAMP
        WHERE truck_id = v_shift_record.truck_id 
          AND status = 'active'
          AND id != v_shift_record.id;
        
        -- Activate the new shift
        UPDATE driver_shifts 
        SET status = 'active', updated_at = CURRENT_TIMESTAMP
        WHERE id = v_shift_record.id;
        
        v_activated_count := v_activated_count + 1;
    END LOOP;
    
    RETURN v_activated_count;
END;
$$;


--
-- Name: FUNCTION auto_activate_shifts_enhanced(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.auto_activate_shifts_enhanced() IS 'Enhanced auto-activation with date range and recurrence support';


--
-- Name: auto_capture_trip_driver(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.auto_capture_trip_driver() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    driver_info RECORD;
    truck_id INTEGER;
BEGIN
    -- Get truck_id from assignment
    SELECT a.truck_id INTO truck_id
    FROM assignments a
    WHERE a.id = NEW.assignment_id;
    
    -- Only capture driver info if not already set and trip is starting
    IF NEW.performed_by_driver_id IS NULL AND NEW.loading_start_time IS NOT NULL THEN
        -- Capture active driver at the time of loading start
        SELECT * INTO driver_info
        FROM capture_active_driver_for_trip(truck_id, NEW.loading_start_time);
        
        IF FOUND THEN
            NEW.performed_by_driver_id := driver_info.driver_id;
            NEW.performed_by_driver_name := driver_info.driver_name;
            NEW.performed_by_employee_id := driver_info.employee_id;
            NEW.performed_by_shift_id := driver_info.shift_id;
            NEW.performed_by_shift_type := driver_info.shift_type;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;


--
-- Name: auto_complete_shifts_enhanced(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.auto_complete_shifts_enhanced() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_completed_count INTEGER := 0;
BEGIN
    -- Complete shifts that have passed their end time
    UPDATE driver_shifts 
    SET status = 'completed', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'active'
      AND (
        -- Single date shifts
        (recurrence_pattern = 'single' AND shift_date = CURRENT_DATE)
        OR
        -- Date range shifts that are active today
        (recurrence_pattern != 'single' AND is_shift_active_on_date(id, CURRENT_DATE))
      )
      AND CURRENT_TIME > CASE 
          WHEN end_time < start_time 
          THEN end_time + interval '24 hours'
          ELSE end_time 
      END;
    
    GET DIAGNOSTICS v_completed_count = ROW_COUNT;
    RETURN v_completed_count;
END;
$$;


--
-- Name: FUNCTION auto_complete_shifts_enhanced(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.auto_complete_shifts_enhanced() IS 'Enhanced auto-completion with date range and recurrence support';


--
-- Name: auto_populate_driver_from_shift(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.auto_populate_driver_from_shift() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_current_driver_id INTEGER;
BEGIN
    -- Only auto-populate if driver_id is NULL
    IF NEW.driver_id IS NULL THEN
        -- Get current active driver for the truck
        SELECT ds.driver_id INTO v_current_driver_id
        FROM driver_shifts ds
        WHERE ds.truck_id = NEW.truck_id
          AND ds.status = 'active'
          AND ds.shift_date = CURRENT_DATE
          AND CURRENT_TIME BETWEEN ds.start_time AND 
              CASE 
                WHEN ds.end_time < ds.start_time 
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time 
              END
        ORDER BY ds.created_at DESC
        LIMIT 1;
        
        -- If we found an active driver, use it
        IF v_current_driver_id IS NOT NULL THEN
            NEW.driver_id := v_current_driver_id;
            
            -- Add note about auto-assignment
            IF NEW.notes IS NULL OR NEW.notes = '' THEN
                NEW.notes := '[Auto-assigned driver from active shift]';
            ELSE
                NEW.notes := NEW.notes || ' [Auto-assigned driver from active shift]';
            END IF;
        ELSE
            -- No active driver found, leave driver_id as NULL
            -- Add note about missing driver
            IF NEW.notes IS NULL OR NEW.notes = '' THEN
                NEW.notes := '[No active driver found - manual assignment required]';
            ELSE
                NEW.notes := NEW.notes || ' [No active driver found - manual assignment required]';
            END IF;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;


--
-- Name: calculate_shift_end_timestamp(date, time without time zone, boolean); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.calculate_shift_end_timestamp(end_date date, end_time time without time zone, is_overnight boolean) RETURNS timestamp without time zone
    LANGUAGE plpgsql IMMUTABLE
    AS $$
BEGIN
    IF is_overnight THEN
        RETURN (end_date + INTERVAL '1 day')::DATE + end_time;
    ELSE
        RETURN end_date::DATE + end_time;
    END IF;
END;
$$;


--
-- Name: calculate_shift_status(boolean, boolean); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.calculate_shift_status(within_date_range boolean, within_time_window boolean) RETURNS text
    LANGUAGE plpgsql IMMUTABLE
    AS $$
BEGIN
    CASE 
        WHEN within_date_range AND within_time_window THEN
            RETURN 'active';
        WHEN within_date_range AND NOT within_time_window THEN
            RETURN 'scheduled';
        ELSE
            RETURN 'scheduled';
    END CASE;
END;
$$;


--
-- Name: calculate_trip_durations(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.calculate_trip_durations() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  -- Calculate loading duration
  IF NEW.loading_start_time IS NOT NULL AND NEW.loading_end_time IS NOT NULL THEN
    NEW.loading_duration_minutes := EXTRACT(EPOCH FROM (NEW.loading_end_time - NEW.loading_start_time)) / 60;
  END IF;
  
  -- Calculate travel duration
  IF NEW.loading_end_time IS NOT NULL AND NEW.unloading_start_time IS NOT NULL THEN
    NEW.travel_duration_minutes := EXTRACT(EPOCH FROM (NEW.unloading_start_time - NEW.loading_end_time)) / 60;
  END IF;
  
  -- Calculate unloading duration
  IF NEW.unloading_start_time IS NOT NULL AND NEW.unloading_end_time IS NOT NULL THEN
    NEW.unloading_duration_minutes := EXTRACT(EPOCH FROM (NEW.unloading_end_time - NEW.unloading_start_time)) / 60;
  END IF;
  
  -- Calculate total duration
  IF NEW.loading_start_time IS NOT NULL AND NEW.trip_completed_time IS NOT NULL THEN
    NEW.total_duration_minutes := EXTRACT(EPOCH FROM (NEW.trip_completed_time - NEW.loading_start_time)) / 60;
  END IF;
  
  RETURN NEW;
END;
$$;


--
-- Name: can_delete_user_role(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.can_delete_user_role(role_to_check text) RETURNS boolean
    LANGUAGE plpgsql
    AS $$

DECLARE

    user_count INTEGER;

BEGIN

    SELECT COUNT(*) INTO user_count

    FROM users 

    WHERE role = role_to_check::user_role;

    

    RETURN user_count = 0;

END;

$$;


--
-- Name: FUNCTION can_delete_user_role(role_to_check text); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.can_delete_user_role(role_to_check text) IS 'Checks if a role can be safely deleted (no users assigned to it)';


--
-- Name: capture_active_driver_for_trip(integer, timestamp without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.capture_active_driver_for_trip(p_truck_id integer, p_timestamp timestamp without time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(driver_id integer, driver_name character varying, employee_id character varying, shift_id integer, shift_type public.shift_type)
    LANGUAGE plpgsql
    AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          enh.driver_id,
          enh.driver_name,
          enh.employee_id,
          enh.shift_id,
          enh.shift_type
        FROM capture_active_driver_for_trip_enhanced(p_truck_id, p_timestamp) enh;
      END;
      $$;


--
-- Name: FUNCTION capture_active_driver_for_trip(p_truck_id integer, p_timestamp timestamp without time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.capture_active_driver_for_trip(p_truck_id integer, p_timestamp timestamp without time zone) IS 'Captures the active driver for a truck at a specific timestamp for trip history';


--
-- Name: capture_active_driver_for_trip_enhanced(integer, timestamp without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.capture_active_driver_for_trip_enhanced(p_truck_id integer, p_timestamp timestamp without time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(driver_id integer, driver_name character varying, employee_id character varying, shift_id integer, shift_type public.shift_type, display_type public.shift_type)
    LANGUAGE plpgsql
    AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          ds.driver_id,
          d.full_name as driver_name,
          d.employee_id,
          ds.id as shift_id,
          ds.shift_type,
          COALESCE(ds.display_type, ds.shift_type) as display_type
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id = p_truck_id
          AND ds.status = 'active'
          AND (
            -- Single date shifts (backward compatibility)
            (ds.recurrence_pattern = 'single' AND ds.shift_date = p_timestamp::date)
            OR
            -- Date range shifts with recurrence patterns
            (ds.recurrence_pattern != 'single' AND p_timestamp::date BETWEEN ds.start_date AND ds.end_date
              AND (
                (ds.recurrence_pattern = 'daily')
                OR
                (ds.recurrence_pattern = 'weekly' AND EXTRACT(DOW FROM p_timestamp::date) = EXTRACT(DOW FROM ds.start_date))
                OR
                (ds.recurrence_pattern = 'weekdays' AND EXTRACT(DOW FROM p_timestamp::date) BETWEEN 1 AND 5)
                OR
                (ds.recurrence_pattern = 'weekends' AND EXTRACT(DOW FROM p_timestamp::date) IN (0, 6))
                OR
                (ds.recurrence_pattern = 'custom')
              )
            )
          )
          AND p_timestamp::time BETWEEN ds.start_time AND 
              CASE 
                WHEN ds.end_time < ds.start_time 
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time 
              END
        ORDER BY ds.created_at DESC
        LIMIT 1;
      END;
      $$;


--
-- Name: capture_active_driver_for_trip_enhanced_test(integer, timestamp without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.capture_active_driver_for_trip_enhanced_test(p_truck_id integer, p_timestamp timestamp without time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(driver_id integer, driver_name character varying, employee_id character varying, shift_id integer, shift_type public.shift_type, source character varying)
    LANGUAGE plpgsql
    AS $$
        BEGIN
          -- First try to get active driver
          RETURN QUERY
          SELECT 
            ds.driver_id,
            d.full_name as driver_name,
            d.employee_id,
            ds.id as shift_id,
            ds.shift_type,
            'active'::VARCHAR(20) as source
          FROM driver_shifts ds
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.truck_id = p_truck_id
            AND ds.status = 'active'
            AND (
              (ds.start_date IS NULL AND ds.shift_date = p_timestamp::date) OR
              (ds.start_date IS NOT NULL AND p_timestamp::date BETWEEN ds.start_date AND ds.end_date)
            )
            AND p_timestamp::time BETWEEN ds.start_time AND 
                CASE 
                  WHEN ds.end_time < ds.start_time 
                  THEN ds.end_time + interval '24 hours'
                  ELSE ds.end_time 
                END
          ORDER BY ds.created_at DESC
          LIMIT 1;
          
          -- If no active driver found, get most recent completed shift
          IF NOT FOUND THEN
            RETURN QUERY
            SELECT 
              ds.driver_id,
              d.full_name as driver_name,
              d.employee_id,
              ds.id as shift_id,
              ds.shift_type,
              'fallback'::VARCHAR(20) as source
            FROM driver_shifts ds
            JOIN drivers d ON ds.driver_id = d.id
            WHERE ds.truck_id = p_truck_id
              AND ds.status = 'completed'
              AND (
                (ds.shift_type = 'day' AND p_timestamp::time BETWEEN '06:00:00' AND '18:00:00') OR
                (ds.shift_type = 'night' AND (p_timestamp::time >= '18:00:00' OR p_timestamp::time <= '06:00:00'))
              )
            ORDER BY 
              CASE 
                WHEN ds.end_date IS NOT NULL THEN ds.end_date
                ELSE ds.shift_date
              END DESC,
              ds.created_at DESC
            LIMIT 1;
          END IF;
        END;
        $$;


--
-- Name: check_shift_status_consistency(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.check_shift_status_consistency() RETURNS TABLE(shift_id integer, current_status public.shift_status, expected_status text, truck_id integer, driver_id integer, shift_type public.shift_type, start_date date, end_date date, start_time time without time zone, end_time time without time zone, issue_description text)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift RECORD;
    v_expected_status TEXT;
BEGIN
    -- Check all shifts for status consistency
    FOR v_shift IN
        SELECT 
            ds.id, 
            ds.truck_id, 
            ds.driver_id, 
            ds.shift_type, 
            ds.status, 
            ds.start_date, 
            ds.end_date, 
            ds.start_time, 
            ds.end_time
        FROM driver_shifts ds
        WHERE ds.status IN ('scheduled', 'active', 'completed')
    LOOP
        -- Get the expected status using the 2-parameter function
        v_expected_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP);
        
        -- If there's a mismatch, report it
        IF v_expected_status != v_shift.status::TEXT THEN
            RETURN QUERY SELECT 
                v_shift.id,
                v_shift.status,
                v_expected_status,
                v_shift.truck_id,
                v_shift.driver_id,
                v_shift.shift_type,
                v_shift.start_date,
                v_shift.end_date,
                v_shift.start_time,
                v_shift.end_time,
                CASE 
                    WHEN v_shift.status = 'completed' AND v_expected_status != 'completed' THEN 
                        'Shift incorrectly marked as completed'
                    WHEN v_shift.status = 'scheduled' AND v_expected_status = 'active' THEN 
                        'Shift should be active but is still scheduled'
                    WHEN v_shift.status = 'active' AND v_expected_status = 'completed' THEN 
                        'Shift should be completed but is still active'
                    ELSE 
                        'Status mismatch detected'
                END;
        END IF;
    END LOOP;
END;
$$;


--
-- Name: FUNCTION check_shift_status_consistency(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.check_shift_status_consistency() IS 'Checks for shift status inconsistencies - uses 2-parameter evaluate_shift_status';


--
-- Name: classify_shift_by_time(time without time zone, time without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.classify_shift_by_time(p_start_time time without time zone, p_end_time time without time zone) RETURNS public.shift_type
    LANGUAGE plpgsql
    AS $$

DECLARE

    v_start_minutes INTEGER;

    v_end_minutes INTEGER;

    v_tolerance INTEGER := 30; -- 30 minutes tolerance

    v_current_hour INTEGER;

BEGIN

    -- If end_time is NULL (active shift), classify based on start_time and current time

    IF p_end_time IS NULL THEN

        v_current_hour := EXTRACT(HOUR FROM CURRENT_TIME);

        

        -- If starting during typical day hours (6 AM - 6 PM), classify as day

        IF EXTRACT(HOUR FROM p_start_time) BETWEEN 6 AND 18 THEN

            RETURN 'day';

        -- If starting during typical night hours (6 PM - 6 AM), classify as night

        ELSIF EXTRACT(HOUR FROM p_start_time) >= 18 OR EXTRACT(HOUR FROM p_start_time) <= 6 THEN

            RETURN 'night';

        ELSE

            RETURN 'custom';

        END IF;

    END IF;

    

    -- Convert times to minutes since midnight for completed shifts

    v_start_minutes := EXTRACT(HOUR FROM p_start_time) * 60 + EXTRACT(MINUTE FROM p_start_time);

    v_end_minutes := EXTRACT(HOUR FROM p_end_time) * 60 + EXTRACT(MINUTE FROM p_end_time);

    

    -- Check for standard day shift patterns (6AM-6PM, 7AM-7PM, 8AM-5PM, etc.)

    IF (

        (ABS(v_start_minutes - 360) <= v_tolerance AND ABS(v_end_minutes - 1080) <= v_tolerance) OR -- 6AM-6PM

        (ABS(v_start_minutes - 420) <= v_tolerance AND ABS(v_end_minutes - 1140) <= v_tolerance) OR -- 7AM-7PM

        (ABS(v_start_minutes - 480) <= v_tolerance AND ABS(v_end_minutes - 1020) <= v_tolerance) OR -- 8AM-5PM

        (ABS(v_start_minutes - 360) <= v_tolerance AND ABS(v_end_minutes - 1020) <= v_tolerance) OR -- 6AM-5PM

        (ABS(v_start_minutes - 420) <= v_tolerance AND ABS(v_end_minutes - 1080) <= v_tolerance) OR -- 7AM-6PM

        (ABS(v_start_minutes - 480) <= v_tolerance AND ABS(v_end_minutes - 960) <= v_tolerance) OR  -- 8AM-4PM

        (ABS(v_start_minutes - 540) <= v_tolerance AND ABS(v_end_minutes - 1020) <= v_tolerance)    -- 9AM-5PM

    ) THEN

        RETURN 'day';

    END IF;

    

    -- Check for standard night shift patterns (6PM-6AM, 7PM-7AM, etc.)

    -- Handle midnight crossing

    IF (

        (ABS(v_start_minutes - 1080) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 360) <= v_tolerance)) OR -- 6PM-6AM

        (ABS(v_start_minutes - 1140) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 420) <= v_tolerance)) OR -- 7PM-7AM

        (ABS(v_start_minutes - 1320) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 360) <= v_tolerance)) OR -- 10PM-6AM

        (ABS(v_start_minutes - 1380) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 420) <= v_tolerance)) OR -- 11PM-7AM

        (ABS(v_start_minutes - 1200) <= v_tolerance AND (v_end_minutes < v_start_minutes AND ABS(v_end_minutes - 480) <= v_tolerance))    -- 8PM-8AM

    ) THEN

        RETURN 'night';

    END IF;

    

    -- If no standard pattern matches, return custom

    RETURN 'custom';

END;

$$;


--
-- Name: FUNCTION classify_shift_by_time(p_start_time time without time zone, p_end_time time without time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.classify_shift_by_time(p_start_time time without time zone, p_end_time time without time zone) IS 'Intelligently classify shift type based on time patterns. Handles NULL end_time for active shifts.';


--
-- Name: cleanup_driver_status_audit(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.cleanup_driver_status_audit(retention_days integer DEFAULT 90) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM driver_status_audit 
    WHERE blocked_at < NOW() - INTERVAL '1 day' * retention_days;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$;


--
-- Name: FUNCTION cleanup_driver_status_audit(retention_days integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.cleanup_driver_status_audit(retention_days integer) IS 'Function to clean up old audit records. Default retention is 90 days.';


--
-- Name: cleanup_security_logs(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.cleanup_security_logs() RETURNS integer
    LANGUAGE plpgsql
    AS $$

DECLARE

    deleted_count INTEGER;

BEGIN

    -- Delete logs older than 90 days (adjust as needed)

    DELETE FROM security_logs 

    WHERE created_at < CURRENT_DATE - INTERVAL '90 days';

    

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    

    -- Log the cleanup operation

    INSERT INTO security_logs (activity_type, details, created_at)

    VALUES ('LOG_CLEANUP', jsonb_build_object('deleted_count', deleted_count), CURRENT_TIMESTAMP);

    

    RETURN deleted_count;

END;

$$;


--
-- Name: FUNCTION cleanup_security_logs(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.cleanup_security_logs() IS 'Automated cleanup function for security log retention policy';


--
-- Name: cleanup_shift_system(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.cleanup_shift_system() RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_cleaned_count INTEGER := 0;
    v_result TEXT;
BEGIN
    -- Remove any problematic 3-parameter functions
    BEGIN
        DROP FUNCTION IF EXISTS fix_incorrectly_completed_shifts(INTEGER, TIMESTAMP, BOOLEAN);
        DROP FUNCTION IF EXISTS check_shift_status_consistency(INTEGER, TIMESTAMP, BOOLEAN);
        DROP FUNCTION IF EXISTS evaluate_shift_status(INTEGER, TIMESTAMP, BOOLEAN);
        v_cleaned_count := v_cleaned_count + 1;
    EXCEPTION WHEN OTHERS THEN
        -- Functions may not exist, continue
    END;
    
    -- Run auto-activation to fix any status inconsistencies
    PERFORM schedule_auto_activation();
    v_cleaned_count := v_cleaned_count + 1;
    
    v_result := 'Cleanup completed: ' || v_cleaned_count || ' operations performed';
    RAISE NOTICE '%', v_result;
    
    RETURN v_result;
END;
$$;


--
-- Name: FUNCTION cleanup_shift_system(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.cleanup_shift_system() IS 'Automatic cleanup function to fix common shift system issues';


--
-- Name: complete_shift_manually(integer, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.complete_shift_manually(p_shift_id integer, p_completed_by integer DEFAULT NULL::integer) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift_exists BOOLEAN;
BEGIN
    -- Check if shift exists and is active
    SELECT EXISTS(
        SELECT 1 FROM driver_shifts
        WHERE id = p_shift_id AND status = 'active'
    ) INTO v_shift_exists;
    
    IF NOT v_shift_exists THEN
        RETURN FALSE;
    END IF;
    
    -- Manually complete the shift
    UPDATE driver_shifts
    SET status = 'completed',
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_shift_id;
    
    -- Log the manual completion
    PERFORM log_system_event(
        'SHIFT_MANUAL_COMPLETION',
        'Shift manually completed by user',
        jsonb_build_object(
            'shift_id', p_shift_id,
            'completed_by', p_completed_by,
            'completion_time', CURRENT_TIMESTAMP
        ),
        p_completed_by
    );
    
    RETURN TRUE;
END;
$$;


--
-- Name: FUNCTION complete_shift_manually(p_shift_id integer, p_completed_by integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.complete_shift_manually(p_shift_id integer, p_completed_by integer) IS 'Manually complete a shift - no automatic completion allowed';


--
-- Name: create_assignment_with_auto_driver(character varying, integer, integer, integer, date, character varying, integer, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_assignment_with_auto_driver(p_assignment_code character varying, p_truck_id integer, p_loading_location_id integer, p_unloading_location_id integer, p_assigned_date date DEFAULT CURRENT_DATE, p_priority character varying DEFAULT 'normal'::character varying, p_expected_loads integer DEFAULT 1, p_notes text DEFAULT NULL::text) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_assignment_id INTEGER;
    v_driver_id INTEGER;
    v_final_notes TEXT;
BEGIN
    -- Get current active driver
    v_driver_id := get_current_active_driver(p_truck_id);
    
    -- Prepare notes
    v_final_notes := COALESCE(p_notes, '');
    IF v_driver_id IS NOT NULL THEN
        v_final_notes := v_final_notes || ' [Driver auto-assigned from active shift]';
    END IF;
    
    -- Create the assignment
    INSERT INTO assignments (
        assignment_code, truck_id, driver_id,
        loading_location_id, unloading_location_id,
        assigned_date, status, priority,
        expected_loads_per_day, notes,
        created_at, updated_at
    ) VALUES (
        p_assignment_code, p_truck_id, v_driver_id,
        p_loading_location_id, p_unloading_location_id,
        p_assigned_date, 'assigned', p_priority,
        p_expected_loads, v_final_notes,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    ) RETURNING id INTO v_assignment_id;
    
    RETURN v_assignment_id;
END;
$$;


--
-- Name: create_deviation_assignment(integer, integer, integer, integer, character varying, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_deviation_assignment(p_truck_id integer, p_driver_id integer, p_loading_location_id integer, p_unloading_location_id integer, p_priority character varying DEFAULT 'normal'::character varying, p_expected_loads integer DEFAULT 1) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_assignment_id INTEGER;
    v_assignment_code VARCHAR(50);
BEGIN
    -- Check if assignment already exists for today
    SELECT id INTO v_assignment_id
    FROM assignments
    WHERE truck_id = p_truck_id 
      AND driver_id = p_driver_id 
      AND loading_location_id = p_loading_location_id
      AND assigned_date = CURRENT_DATE
      AND status IN ('assigned', 'in_progress')
    LIMIT 1;

    -- If exists, return existing ID
    IF v_assignment_id IS NOT NULL THEN
        RETURN v_assignment_id;
    END IF;

    -- Generate unique assignment code
    v_assignment_code := 'ASG-' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD-HH24MISS') || '-AUTO';

    -- Create new assignment
    INSERT INTO assignments (
        assignment_code, truck_id, driver_id, 
        loading_location_id, unloading_location_id, 
        assigned_date, status, priority, 
        expected_loads_per_day, notes, created_at, updated_at
    )
    VALUES (
        v_assignment_code, p_truck_id, p_driver_id,
        p_loading_location_id, p_unloading_location_id,
        CURRENT_DATE, 'assigned', p_priority,
        p_expected_loads, '[Auto-created for route deviation]', 
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    RETURNING id INTO v_assignment_id;

    RETURN v_assignment_id;
END;
$$;


--
-- Name: FUNCTION create_deviation_assignment(p_truck_id integer, p_driver_id integer, p_loading_location_id integer, p_unloading_location_id integer, p_priority character varying, p_expected_loads integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.create_deviation_assignment(p_truck_id integer, p_driver_id integer, p_loading_location_id integer, p_unloading_location_id integer, p_priority character varying, p_expected_loads integer) IS 'Creates or returns assignment for route deviation scenarios';


--
-- Name: create_shift_assignment(integer, integer, integer, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_shift_assignment(p_truck_id integer, p_shift_id integer, p_loading_location_id integer, p_unloading_location_id integer) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_assignment_id INTEGER;
    v_driver_id INTEGER;
    v_assignment_code VARCHAR(50);
BEGIN
    -- Get driver from shift
    SELECT driver_id INTO v_driver_id
    FROM driver_shifts
    WHERE id = p_shift_id;
    
    IF v_driver_id IS NULL THEN
        RAISE EXCEPTION 'Invalid shift ID: %', p_shift_id;
    END IF;
    
    -- Generate assignment code
    v_assignment_code := 'SHIFT-' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDD-HH24MISS') || '-' || p_shift_id;
    
    -- Create assignment
    INSERT INTO assignments (
        assignment_code, truck_id, driver_id,
        loading_location_id, unloading_location_id,
        shift_id, is_shift_assignment,
        assigned_date, status, priority,
        expected_loads_per_day, notes
    ) VALUES (
        v_assignment_code, p_truck_id, v_driver_id,
        p_loading_location_id, p_unloading_location_id,
        p_shift_id, true,
        CURRENT_DATE, 'assigned', 'normal',
        1, 'Auto-created for shift-based assignment'
    ) RETURNING id INTO v_assignment_id;
    
    -- Update shift with assignment reference
    UPDATE driver_shifts 
    SET assignment_id = v_assignment_id,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_shift_id;
    
    RETURN v_assignment_id;
END;
$$;


--
-- Name: create_unified_shift(integer, integer, public.shift_type, date, date, time without time zone, time without time zone, public.shift_status, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_unified_shift(p_truck_id integer, p_driver_id integer, p_shift_type public.shift_type, p_start_date date, p_end_date date, p_start_time time without time zone, p_end_time time without time zone, p_status public.shift_status DEFAULT 'scheduled'::public.shift_status, p_handover_notes text DEFAULT ''::text) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift_id INTEGER;
BEGIN
    INSERT INTO driver_shifts (
        truck_id,
        driver_id,
        shift_type,
        shift_date,      -- Will be auto-set to start_date by trigger
        start_date,
        end_date,
        start_time,
        end_time,
        status,
        handover_notes,
        recurrence_pattern
    ) VALUES (
        p_truck_id,
        p_driver_id,
        p_shift_type,
        p_start_date,    -- This will be synced by trigger
        p_start_date,
        p_end_date,
        p_start_time,
        p_end_time,
        p_status,
        p_handover_notes,
        CASE WHEN p_start_date = p_end_date THEN 'single' ELSE 'custom' END
    ) RETURNING id INTO v_shift_id;
    
    RETURN v_shift_id;
END;
$$;


--
-- Name: debug_shift_status(integer, timestamp with time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.debug_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(shift_id integer, shift_start_date date, shift_end_date date, shift_start_time time without time zone, shift_end_time time without time zone, current_date_val date, current_time_val time without time zone, is_overnight boolean, is_within_date_range boolean, is_within_time_window boolean, shift_end_datetime timestamp without time zone, is_past_completion boolean, calculated_status text)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift RECORD;
    v_current_date DATE;
    v_current_time TIME;
    v_is_overnight BOOLEAN;
    v_is_within_date_range BOOLEAN;
    v_is_within_time_window BOOLEAN;
    v_is_past_completion BOOLEAN;
    v_new_status TEXT;
    v_shift_end_datetime TIMESTAMP;
BEGIN
    -- Get shift details
    SELECT
        id,
        start_date,
        end_date,
        start_time,
        end_time,
        shift_type,
        status,
        recurrence_pattern
    INTO v_shift
    FROM driver_shifts
    WHERE id = p_shift_id;

    IF NOT FOUND THEN
        RETURN;
    END IF;

    -- Extract current date and time from reference timestamp
    v_current_date := p_reference_timestamp::DATE;
    v_current_time := p_reference_timestamp::TIME;

    -- Check if shift spans overnight (night shift logic)
    v_is_overnight := v_shift.end_time < v_shift.start_time;

    -- Enhanced date range validation with unified approach
    v_is_within_date_range := v_current_date BETWEEN v_shift.start_date AND v_shift.end_date;

    -- Enhanced time window validation with proper overnight logic
    IF v_is_overnight THEN
        -- Night shift: Use dual condition logic for overnight spans
        v_is_within_time_window := (v_current_time >= v_shift.start_time OR v_current_time <= v_shift.end_time);
    ELSE
        -- Day shift: Use simple BETWEEN logic for same-day shifts
        v_is_within_time_window := (v_current_time BETWEEN v_shift.start_time AND v_shift.end_time);
    END IF;

    -- Enhanced completion logic with proper overnight handling (FOR DEBUG ONLY)
    IF v_is_overnight THEN
        -- For overnight shifts: would be completed when past end_time on the next day
        v_shift_end_datetime := (v_shift.end_date + INTERVAL '1 day')::DATE + v_shift.end_time;
        v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;
    ELSE
        -- For day shifts: would be completed when past end_time on the same day
        v_shift_end_datetime := v_shift.end_date::DATE + v_shift.end_time;
        v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;
    END IF;

    -- CORRECTED: Apply status rules with MANUAL-ONLY completion
    -- DEBUG NOTE: This shows what the status WOULD be, but completion is MANUAL ONLY
    IF v_is_within_date_range AND v_is_within_time_window THEN
        -- Rule 1: Active - within date range AND within time window
        v_new_status := 'active';
    ELSIF v_is_within_date_range AND NOT v_is_within_time_window THEN
        -- Rule 2: Scheduled - within date range BUT outside time window
        v_new_status := 'scheduled';
    ELSE
        -- Default: Scheduled for future dates
        -- NOTE: Even if past completion time, we return 'scheduled' because completion is MANUAL ONLY
        v_new_status := 'scheduled';
    END IF;

    -- Return all debug information with explicit variable references
    RETURN QUERY SELECT
        v_shift.id::INTEGER,
        v_shift.start_date::DATE,
        v_shift.end_date::DATE,
        v_shift.start_time::TIME,
        v_shift.end_time::TIME,
        v_current_date::DATE,
        v_current_time::TIME,
        v_is_overnight::BOOLEAN,
        v_is_within_date_range::BOOLEAN,
        v_is_within_time_window::BOOLEAN,
        v_shift_end_datetime::TIMESTAMP,
        v_is_past_completion::BOOLEAN,
        v_new_status::TEXT;
END;
$$;


--
-- Name: FUNCTION debug_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.debug_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone) IS 'Debug function for shift status evaluation - shows calculated status with MANUAL-ONLY completion policy. Even if past completion time, only returns active/scheduled since completion requires manual user action.';


--
-- Name: determine_shift_type_by_time(time without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.determine_shift_type_by_time(p_start_time time without time zone) RETURNS public.shift_type
    LANGUAGE plpgsql
    AS $$

BEGIN

    -- Day shift: 06:00 AM to 06:00 PM (hours 6-18)

    IF EXTRACT(HOUR FROM p_start_time) BETWEEN 6 AND 18 THEN

        RETURN 'day';

    -- Night shift: 06:01 PM to 05:59 AM (hours 19-23 OR 0-5)

    ELSIF EXTRACT(HOUR FROM p_start_time) >= 19 OR EXTRACT(HOUR FROM p_start_time) <= 5 THEN

        RETURN 'night';

    ELSE

        RETURN 'custom';

    END IF;

END;

$$;


--
-- Name: FUNCTION determine_shift_type_by_time(p_start_time time without time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.determine_shift_type_by_time(p_start_time time without time zone) IS 'Updated shift type determination: Day shift 6AM-6PM (hours 6-18), Night shift 6:01PM-5:59AM (hours 19-23 OR 0-5)';


--
-- Name: evaluate_shift_status(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.evaluate_shift_status(p_shift_id integer) RETURNS text
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN evaluate_shift_status(p_shift_id, CURRENT_TIMESTAMP);
END;
$$;


--
-- Name: FUNCTION evaluate_shift_status(p_shift_id integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.evaluate_shift_status(p_shift_id integer) IS 'Backward compatibility wrapper';


--
-- Name: evaluate_shift_status(integer, timestamp with time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.evaluate_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone DEFAULT CURRENT_TIMESTAMP) RETURNS text
    LANGUAGE plpgsql
    AS $$

DECLARE

    v_shift_type TEXT;

    v_start_date DATE;

    v_end_date DATE;

    v_start_time TIME;

    v_end_time TIME;

    v_current_status TEXT;

    v_auto_created BOOLEAN;

    v_current_date DATE;

    v_current_time TIME;

    v_is_overnight BOOLEAN;

    v_is_within_date_range BOOLEAN;

    v_is_within_time_window BOOLEAN;

    v_shift_end_datetime TIMESTAMP;

    v_is_past_completion BOOLEAN;

BEGIN

    -- Get shift details including auto_created flag

    SELECT 

        shift_type,

        start_date,

        end_date,

        start_time,

        end_time,

        status::TEXT,

        auto_created,

        start_time > COALESCE(end_time, '23:59:59'::TIME)

    INTO

        v_shift_type,

        v_start_date,

        v_end_date,

        v_start_time,

        v_end_time,

        v_current_status,

        v_auto_created,

        v_is_overnight

    FROM driver_shifts

    WHERE id = p_shift_id;

    

    -- If shift not found or cancelled, return error

    IF v_shift_type IS NULL OR v_current_status = 'cancelled' THEN

        RETURN 'error';

    END IF;

    

    -- If shift is already completed, keep it completed

    IF v_current_status = 'completed' THEN

        RETURN 'completed';

    END IF;

    

    -- For QR-created shifts (auto_created = true) with NULL end_time, keep them active

    -- This handles the case where drivers scan QR codes to start shifts

    IF v_auto_created = true AND v_end_time IS NULL AND v_current_status = 'active' THEN

        RETURN 'active';

    END IF;

    

    -- Extract date and time from reference timestamp

    v_current_date := p_reference_timestamp::DATE;

    v_current_time := p_reference_timestamp::TIME;

    

    -- If end_time is NULL, we can't calculate completion, so keep current status if active

    IF v_end_time IS NULL THEN

        IF v_current_status = 'active' THEN

            RETURN 'active';

        ELSE

            RETURN 'scheduled';

        END IF;

    END IF;

    

    -- Calculate the actual shift end datetime for proper completion logic

    IF v_is_overnight THEN

        -- For overnight shifts: end_time on the day after end_date

        v_shift_end_datetime := (v_end_date + INTERVAL '1 day')::DATE + v_end_time;

    ELSE

        -- For day shifts: end_time on the same day as end_date

        v_shift_end_datetime := v_end_date::DATE + v_end_time;

    END IF;

    

    -- Check if we're past the shift's actual end datetime

    v_is_past_completion := p_reference_timestamp > v_shift_end_datetime;

    

    -- Apply business rules for status determination

    IF v_is_past_completion THEN

        -- Shift has ended (both date and time conditions met)

        RETURN 'completed';

    ELSIF v_current_date BETWEEN v_start_date AND v_end_date THEN

        -- Shift is within its date range

        IF v_is_overnight THEN

            -- Night shift: active if time >= start_time OR time <= end_time

            v_is_within_time_window := (v_current_time >= v_start_time OR v_current_time <= v_end_time);

        ELSE

            -- Day shift: active if time between start_time and end_time

            v_is_within_time_window := (v_current_time BETWEEN v_start_time AND v_end_time);

        END IF;

        

        IF v_is_within_time_window THEN

            RETURN 'active';

        ELSE

            RETURN 'scheduled';

        END IF;

    ELSE

        -- Future shifts

        RETURN 'scheduled';

    END IF;

END;

$$;


--
-- Name: FUNCTION evaluate_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.evaluate_shift_status(p_shift_id integer, p_reference_timestamp timestamp with time zone) IS 'Enhanced shift status evaluation that properly handles QR-created shifts with NULL end_time';


--
-- Name: fix_incorrectly_completed_shifts(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.fix_incorrectly_completed_shifts() RETURNS TABLE(shift_id integer, old_status public.shift_status, new_status public.shift_status, truck_id integer, driver_id integer, shift_type public.shift_type, start_date date, end_date date)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift RECORD;
    v_correct_status TEXT;
    v_fixed_count INTEGER := 0;
BEGIN
    -- Find all completed shifts and check if they should actually be completed
    FOR v_shift IN
        SELECT ds.id, ds.truck_id, ds.driver_id, ds.shift_type, ds.status, ds.start_date, ds.end_date
        FROM driver_shifts ds
        WHERE ds.status = 'completed'
    LOOP
        -- Get the correct status using the 2-parameter function
        v_correct_status := evaluate_shift_status(v_shift.id, CURRENT_TIMESTAMP);
        
        -- If the correct status is different from current status, fix it
        IF v_correct_status != v_shift.status::TEXT THEN
            UPDATE driver_shifts
            SET 
                status = v_correct_status::shift_status,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = v_shift.id;
            
            v_fixed_count := v_fixed_count + 1;
            
            -- Return the fixed shift information
            RETURN QUERY SELECT 
                v_shift.id,
                v_shift.status,
                v_correct_status::shift_status,
                v_shift.truck_id,
                v_shift.driver_id,
                v_shift.shift_type,
                v_shift.start_date,
                v_shift.end_date;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Fixed % incorrectly completed shifts', v_fixed_count;
END;
$$;


--
-- Name: FUNCTION fix_incorrectly_completed_shifts(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.fix_incorrectly_completed_shifts() IS 'Fixes shifts that are incorrectly marked as completed - uses 2-parameter evaluate_shift_status';


--
-- Name: get_active_driver_shift_for_truck(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_active_driver_shift_for_truck(p_truck_id integer) RETURNS TABLE(shift_id integer, driver_id integer, employee_id character varying, full_name character varying, start_date date, start_time time without time zone)
    LANGUAGE plpgsql
    AS $$

BEGIN

    RETURN QUERY

    SELECT 

        ds.id as shift_id,

        ds.driver_id,

        d.employee_id,

        d.full_name,

        ds.start_date,

        ds.start_time

    FROM driver_shifts ds

    JOIN drivers d ON ds.driver_id = d.id

    WHERE ds.truck_id = p_truck_id 

      AND ds.status = 'active'

      AND d.status = 'active'

    ORDER BY ds.created_at DESC

    LIMIT 1;

END;

$$;


--
-- Name: FUNCTION get_active_driver_shift_for_truck(p_truck_id integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_active_driver_shift_for_truck(p_truck_id integer) IS 'Returns active driver shift information for a specific truck';


--
-- Name: get_active_shifts_for_date(date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_active_shifts_for_date(p_check_date date DEFAULT CURRENT_DATE) RETURNS TABLE(shift_id integer, truck_id integer, driver_id integer, shift_type public.shift_type, display_type public.shift_type, recurrence_pattern public.recurrence_pattern, start_time time without time zone, end_time time without time zone, status public.shift_status)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ds.id as shift_id,
        ds.truck_id,
        ds.driver_id,
        ds.shift_type,
        COALESCE(ds.display_type, ds.shift_type) as display_type,
        ds.recurrence_pattern,
        ds.start_time,
        ds.end_time,
        ds.status
    FROM driver_shifts ds
    WHERE ds.status IN ('scheduled', 'active')
      AND (
        -- Single date shifts (backward compatibility)
        (ds.recurrence_pattern = 'single' AND ds.shift_date = p_check_date)
        OR
        -- Date range shifts with recurrence patterns
        (ds.recurrence_pattern != 'single' AND is_shift_active_on_date(ds.id, p_check_date))
      )
    ORDER BY ds.truck_id, ds.start_time;
END;
$$;


--
-- Name: FUNCTION get_active_shifts_for_date(p_check_date date); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_active_shifts_for_date(p_check_date date) IS 'Get all active shifts for a date with recurrence pattern support';


--
-- Name: get_advanced_exception_analytics(date, date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_advanced_exception_analytics(p_start_date date DEFAULT (CURRENT_DATE - '30 days'::interval), p_end_date date DEFAULT CURRENT_DATE) RETURNS TABLE(metric_category character varying, metric_name character varying, metric_value numeric, metric_unit character varying, metric_trend character varying)
    LANGUAGE plpgsql
    AS $$
BEGIN
  RETURN QUERY
  WITH current_period AS (
    SELECT 
      COUNT(*) as total_exceptions,
      COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_exceptions,
      COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_exceptions,
      COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_exceptions,
      COUNT(CASE WHEN exception_type = 'route_deviation' THEN 1 END) as route_deviations,
      AVG(CASE 
        WHEN status != 'pending' AND reviewed_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (reviewed_at - requested_at))/3600 
      END) as avg_resolution_hours
    FROM approvals
    WHERE created_at BETWEEN p_start_date AND p_end_date
  ),
  previous_period AS (
    SELECT 
      COUNT(*) as total_exceptions,
      COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_exceptions,
      AVG(CASE 
        WHEN status != 'pending' AND reviewed_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (reviewed_at - requested_at))/3600 
      END) as avg_resolution_hours
    FROM approvals
    WHERE created_at BETWEEN (p_start_date - (p_end_date - p_start_date)) AND p_start_date
  )
  SELECT 'exceptions'::VARCHAR(50), 'total_count', cp.total_exceptions::NUMERIC, 'count'::VARCHAR(20),
    CASE 
      WHEN pp.total_exceptions = 0 THEN 'new'
      WHEN cp.total_exceptions > pp.total_exceptions THEN 'increasing'
      WHEN cp.total_exceptions < pp.total_exceptions THEN 'decreasing'
      ELSE 'stable'
    END::VARCHAR(20)
  FROM current_period cp, previous_period pp
  
  UNION ALL
  
  SELECT 'exceptions', 'pending_count', cp.pending_exceptions::NUMERIC, 'count',
    CASE WHEN cp.pending_exceptions > 5 THEN 'high' ELSE 'normal' END
  FROM current_period cp
  
  UNION ALL
  
  SELECT 'exceptions', 'approval_rate', 
    ROUND(cp.approved_exceptions::NUMERIC / NULLIF(cp.total_exceptions, 0) * 100, 2), 'percentage',
    CASE 
      WHEN cp.approved_exceptions::NUMERIC / NULLIF(cp.total_exceptions, 0) > 0.8 THEN 'good'
      WHEN cp.approved_exceptions::NUMERIC / NULLIF(cp.total_exceptions, 0) > 0.6 THEN 'moderate'
      ELSE 'low'
    END
  FROM current_period cp
  
  UNION ALL
  
  SELECT 'performance', 'avg_resolution_time', 
    ROUND(cp.avg_resolution_hours::NUMERIC, 2), 'hours',
    CASE 
      WHEN cp.avg_resolution_hours < 2 THEN 'excellent'
      WHEN cp.avg_resolution_hours < 8 THEN 'good' 
      WHEN cp.avg_resolution_hours < 24 THEN 'acceptable'
      ELSE 'slow'
    END
  FROM current_period cp
  
  UNION ALL
  
  SELECT 'patterns', 'route_deviation_rate',
    ROUND(cp.route_deviations::NUMERIC / NULLIF(cp.total_exceptions, 0) * 100, 2), 'percentage',
    CASE 
      WHEN cp.route_deviations::NUMERIC / NULLIF(cp.total_exceptions, 0) > 0.5 THEN 'high'
      WHEN cp.route_deviations::NUMERIC / NULLIF(cp.total_exceptions, 0) > 0.3 THEN 'moderate'
      ELSE 'low'
    END
  FROM current_period cp;
END;
$$;


--
-- Name: FUNCTION get_advanced_exception_analytics(p_start_date date, p_end_date date); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_advanced_exception_analytics(p_start_date date, p_end_date date) IS 'Comprehensive exception analytics with trend analysis';


--
-- Name: get_current_active_driver(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_current_active_driver(p_truck_id integer) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_driver_id INTEGER;
BEGIN
    -- Get the current active driver for the truck from shift management
    SELECT ds.driver_id INTO v_driver_id
    FROM driver_shifts ds
    WHERE ds.truck_id = p_truck_id
      AND ds.status = 'active'
      AND (
        -- Day shift or night shift that doesn't cross midnight
        (ds.shift_date = CURRENT_DATE AND CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
        OR
        -- Night shift that crosses midnight - started yesterday, still active today
        (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND 
         ds.shift_date = CURRENT_DATE - 1 AND CURRENT_TIME <= ds.end_time)
        OR
        -- Night shift that crosses midnight - started today
        (ds.shift_type = 'night' AND ds.end_time < ds.start_time AND 
         ds.shift_date = CURRENT_DATE AND CURRENT_TIME >= ds.start_time)
      )
    ORDER BY ds.created_at DESC
    LIMIT 1;
    
    RETURN v_driver_id;
END;
$$;


--
-- Name: get_current_driver_for_truck(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_current_driver_for_truck(p_truck_id integer) RETURNS TABLE(driver_id integer, driver_name character varying, shift_id integer, shift_type public.shift_type, shift_start time without time zone, shift_end time without time zone)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id as driver_id,
        d.full_name as driver_name,
        ds.id as shift_id,
        ds.shift_type,
        ds.start_time as shift_start,
        ds.end_time as shift_end
    FROM driver_shifts ds
    JOIN drivers d ON ds.driver_id = d.id
    WHERE ds.truck_id = p_truck_id
        AND ds.status = 'active'
        AND ds.shift_date = CURRENT_DATE
        AND CURRENT_TIME BETWEEN ds.start_time AND 
            CASE 
                WHEN ds.end_time < ds.start_time 
                THEN ds.end_time + interval '24 hours'
                ELSE ds.end_time 
            END
    ORDER BY ds.created_at DESC
    LIMIT 1;
END;
$$;


--
-- Name: get_current_driver_for_truck_enhanced(integer, date, time without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_current_driver_for_truck_enhanced(p_truck_id integer, p_check_date date DEFAULT CURRENT_DATE, p_check_time time without time zone DEFAULT CURRENT_TIME) RETURNS TABLE(driver_id integer, driver_name character varying, employee_id character varying, shift_type public.shift_type, display_type public.shift_type, shift_id integer)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ds.driver_id,
        d.full_name as driver_name,
        d.employee_id,
        ds.shift_type,
        COALESCE(ds.display_type, ds.shift_type) as display_type,
        ds.id as shift_id
    FROM driver_shifts ds
    JOIN drivers d ON ds.driver_id = d.id
    WHERE ds.truck_id = p_truck_id
      AND ds.status = 'active'
      AND is_shift_active_on_date(ds.id, p_check_date)
      AND p_check_time BETWEEN ds.start_time AND 
          CASE 
            WHEN ds.end_time < ds.start_time 
            THEN ds.end_time + interval '24 hours'
            ELSE ds.end_time 
          END
    ORDER BY ds.created_at DESC
    LIMIT 1;
END;
$$;


--
-- Name: FUNCTION get_current_driver_for_truck_enhanced(p_truck_id integer, p_check_date date, p_check_time time without time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_current_driver_for_truck_enhanced(p_truck_id integer, p_check_date date, p_check_time time without time zone) IS 'Get current active driver for truck with date range support';


--
-- Name: get_database_performance_metrics(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_database_performance_metrics() RETURNS TABLE(metric_name character varying, metric_value numeric, metric_unit character varying, recommendation text)
    LANGUAGE plpgsql
    AS $$
BEGIN
  RETURN QUERY
  WITH table_stats AS (
    SELECT 
      schemaname,
      tablename,
      n_tup_ins as inserts,
      n_tup_upd as updates,
      n_tup_del as deletes,
      n_live_tup as live_tuples,
      n_dead_tup as dead_tuples,
      last_vacuum,
      last_autovacuum,
      last_analyze,
      last_autoanalyze
    FROM pg_stat_user_tables
    WHERE schemaname = 'public'
  ),
  index_usage AS (
    SELECT 
      schemaname,
      tablename,
      indexname,
      idx_tup_read,
      idx_tup_fetch
    FROM pg_stat_user_indexes
    WHERE schemaname = 'public'
  )
  SELECT 'table_stats'::VARCHAR(100), 
         SUM(ts.live_tuples)::NUMERIC, 
         'rows'::VARCHAR(20),
         'Total live tuples across all tables'::TEXT
  FROM table_stats ts
  
  UNION ALL
  
  SELECT 'dead_tuples_ratio',
         ROUND(SUM(ts.dead_tuples)::NUMERIC / NULLIF(SUM(ts.live_tuples), 0) * 100, 2),
         'percentage',
         CASE 
           WHEN SUM(ts.dead_tuples)::NUMERIC / NULLIF(SUM(ts.live_tuples), 0) > 0.1 
           THEN 'Consider running VACUUM on heavily updated tables'
           ELSE 'Dead tuple ratio is healthy'
         END
  FROM table_stats ts
  
  UNION ALL
  
  SELECT 'index_efficiency',
         ROUND(SUM(iu.idx_tup_fetch)::NUMERIC / NULLIF(SUM(iu.idx_tup_read), 0) * 100, 2),
         'percentage',
         CASE 
           WHEN SUM(iu.idx_tup_fetch)::NUMERIC / NULLIF(SUM(iu.idx_tup_read), 0) < 0.9
           THEN 'Some indexes may be underutilized'
           ELSE 'Index usage is efficient'
         END
  FROM index_usage iu;
END;
$$;


--
-- Name: get_exception_analytics(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_exception_analytics(p_days integer DEFAULT 30) RETURNS TABLE(metric_name character varying, metric_value numeric, metric_unit character varying)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    WITH date_range AS (
        SELECT CURRENT_DATE - (p_days || ' days')::INTERVAL as start_date
    ),
    metrics AS (
        SELECT 
            COUNT(*) as total_exceptions,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_exceptions,
            COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_exceptions,
            COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_exceptions,
            AVG(CASE 
                WHEN status != 'pending' AND reviewed_at IS NOT NULL 
                THEN EXTRACT(EPOCH FROM (reviewed_at - requested_at))/3600 
            END) as avg_resolution_hours
        FROM approvals, date_range
        WHERE created_at >= date_range.start_date
    ),
    trip_metrics AS (
        SELECT COUNT(*) as total_trips
        FROM trip_logs, date_range
        WHERE created_at >= date_range.start_date
    )
    SELECT 'total_exceptions'::VARCHAR(50), total_exceptions::NUMERIC, 'count'::VARCHAR(20) FROM metrics
    UNION ALL
    SELECT 'pending_exceptions', pending_exceptions::NUMERIC, 'count' FROM metrics
    UNION ALL
    SELECT 'approved_exceptions', approved_exceptions::NUMERIC, 'count' FROM metrics
    UNION ALL
    SELECT 'rejected_exceptions', rejected_exceptions::NUMERIC, 'count' FROM metrics
    UNION ALL
    SELECT 'avg_resolution_time', ROUND(avg_resolution_hours::NUMERIC, 2), 'hours' FROM metrics
    UNION ALL
    SELECT 'total_trips', total_trips::NUMERIC, 'count' FROM trip_metrics
    UNION ALL
    SELECT 'exception_rate', 
           ROUND((SELECT total_exceptions FROM metrics)::NUMERIC / 
                 NULLIF((SELECT total_trips FROM trip_metrics), 0) * 100, 2), 
           'percentage';
END;
$$;


--
-- Name: FUNCTION get_exception_analytics(p_days integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_exception_analytics(p_days integer) IS 'Returns key exception metrics for the specified number of days';


--
-- Name: get_shift_display_date(date, date, date, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_shift_display_date(p_shift_date date, p_start_date date, p_end_date date, p_recurrence_pattern text) RETURNS text
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Unified approach: always use start_date/end_date
    IF p_start_date = p_end_date THEN
        -- Single day shift
        RETURN p_start_date::TEXT;
    ELSE
        -- Multi-day shift
        RETURN p_start_date::TEXT || ' to ' || p_end_date::TEXT;
    END IF;
END;
$$;


--
-- Name: get_shift_status_summary(timestamp without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_shift_status_summary(p_reference_timestamp timestamp without time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(total_shifts integer, active_shifts integer, scheduled_shifts integer, completed_shifts integer, cancelled_shifts integer, needs_activation integer, needs_completion integer, overnight_active integer)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    WITH shift_analysis AS (
        SELECT
            ds.id,
            ds.status as current_status,
            evaluate_shift_status(ds.id, p_reference_timestamp) as calculated_status,
            CASE WHEN ds.end_time < ds.start_time THEN 1 ELSE 0 END as is_overnight
        FROM driver_shifts ds
        WHERE ds.status != 'cancelled'
    )
    SELECT
        COUNT(*)::INTEGER as total_shifts,
        COUNT(CASE WHEN current_status = 'active' THEN 1 END)::INTEGER as active_shifts,
        COUNT(CASE WHEN current_status = 'scheduled' THEN 1 END)::INTEGER as scheduled_shifts,
        COUNT(CASE WHEN current_status = 'completed' THEN 1 END)::INTEGER as completed_shifts,
        COUNT(CASE WHEN current_status = 'cancelled' THEN 1 END)::INTEGER as cancelled_shifts,
        COUNT(CASE WHEN current_status = 'scheduled' AND calculated_status = 'active' THEN 1 END)::INTEGER as needs_activation,
        COUNT(CASE WHEN current_status = 'active' AND calculated_status = 'completed' THEN 1 END)::INTEGER as needs_completion,
        COUNT(CASE WHEN current_status = 'active' AND is_overnight = 1 THEN 1 END)::INTEGER as overnight_active
    FROM shift_analysis;
END;
$$;


--
-- Name: FUNCTION get_shift_status_summary(p_reference_timestamp timestamp without time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_shift_status_summary(p_reference_timestamp timestamp without time zone) IS 'Provides real-time summary of shift statuses for monitoring and validation';


--
-- Name: get_user_roles(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_user_roles() RETURNS TABLE(role_name text)
    LANGUAGE plpgsql
    AS $$

BEGIN

    RETURN QUERY

    SELECT enumlabel::TEXT 

    FROM pg_enum 

    WHERE enumtypid = 'user_role'::regtype

    ORDER BY enumlabel;

END;

$$;


--
-- Name: FUNCTION get_user_roles(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_user_roles() IS 'Returns all values from the user_role enum';


--
-- Name: handover_driver_shift(integer, integer, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.handover_driver_shift(p_truck_id integer, p_new_driver_id integer, p_handover_notes text DEFAULT NULL::text) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_old_shift_id INTEGER;
    v_new_shift_id INTEGER;
    v_current_timestamp TIMESTAMP := CURRENT_TIMESTAMP;
    v_current_date DATE := CURRENT_DATE;
    v_current_time TIME := CURRENT_TIME;
BEGIN
    -- End any existing active shift for this truck
    -- FIXED: Use CURRENT_DATE for end_date (this is correct for database functions)
    -- The bug was in the JavaScript service layer, not in this database function
    UPDATE driver_shifts 
    SET 
        status = 'completed',
        end_date = v_current_date,  -- This is correct - CURRENT_DATE returns actual current date
        end_time = v_current_time,  -- This is correct - CURRENT_TIME returns actual current time
        handover_notes = COALESCE(p_handover_notes, 'Automatic handover via QR system'),
        handover_completed_at = v_current_timestamp,
        updated_at = v_current_timestamp
    WHERE truck_id = p_truck_id 
      AND status = 'active'
    RETURNING id INTO v_old_shift_id;
    
    -- Create new active shift for the new driver with NULL end_date and end_time
    INSERT INTO driver_shifts (
        truck_id,
        driver_id,
        shift_type,
        start_date,
        end_date,
        start_time,
        end_time,
        status,
        previous_shift_id,
        auto_created,
        created_at,
        updated_at
    ) VALUES (
        p_truck_id,
        p_new_driver_id,
        'custom',
        v_current_date,  -- This is correct - CURRENT_DATE returns actual current date
        NULL,            -- NULL for active shifts
        v_current_time,  -- This is correct - CURRENT_TIME returns actual current time
        NULL,            -- NULL for active shifts
        'active',
        v_old_shift_id,
        true,
        v_current_timestamp,
        v_current_timestamp
    ) RETURNING id INTO v_new_shift_id;
    
    RETURN v_new_shift_id;
END;
$$;


--
-- Name: FUNCTION handover_driver_shift(p_truck_id integer, p_new_driver_id integer, p_handover_notes text); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.handover_driver_shift(p_truck_id integer, p_new_driver_id integer, p_handover_notes text) IS 'Handles automatic driver shift handovers - FIXED: Database functions using CURRENT_DATE/CURRENT_TIME are correct for overnight shifts';


--
-- Name: is_overnight_shift(time without time zone, time without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.is_overnight_shift(start_time time without time zone, end_time time without time zone) RETURNS boolean
    LANGUAGE plpgsql IMMUTABLE
    AS $$
BEGIN
    RETURN end_time < start_time;
END;
$$;


--
-- Name: is_shift_active_on_date(integer, date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.is_shift_active_on_date(p_shift_id integer, p_check_date date DEFAULT CURRENT_DATE) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_shift RECORD;
    v_is_active BOOLEAN := FALSE;
BEGIN
    -- Get shift details
    SELECT 
        shift_date, start_date, end_date, recurrence_pattern,
        EXTRACT(DOW FROM start_date) as start_dow
    INTO v_shift
    FROM driver_shifts 
    WHERE id = p_shift_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Check based on recurrence pattern
    CASE v_shift.recurrence_pattern
        WHEN 'single' THEN
            v_is_active := (v_shift.shift_date = p_check_date);
            
        WHEN 'daily' THEN
            v_is_active := (p_check_date BETWEEN v_shift.start_date AND v_shift.end_date);
            
        WHEN 'weekly' THEN
            v_is_active := (
                p_check_date BETWEEN v_shift.start_date AND v_shift.end_date AND
                EXTRACT(DOW FROM p_check_date) = v_shift.start_dow
            );
            
        WHEN 'weekdays' THEN
            v_is_active := (
                p_check_date BETWEEN v_shift.start_date AND v_shift.end_date AND
                EXTRACT(DOW FROM p_check_date) BETWEEN 1 AND 5
            );
            
        WHEN 'weekends' THEN
            v_is_active := (
                p_check_date BETWEEN v_shift.start_date AND v_shift.end_date AND
                EXTRACT(DOW FROM p_check_date) IN (0, 6)
            );
            
        WHEN 'custom' THEN
            -- Custom patterns handled by application logic
            v_is_active := (p_check_date BETWEEN v_shift.start_date AND v_shift.end_date);
            
        ELSE
            v_is_active := FALSE;
    END CASE;
    
    RETURN v_is_active;
END;
$$;


--
-- Name: FUNCTION is_shift_active_on_date(p_shift_id integer, p_check_date date); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.is_shift_active_on_date(p_shift_id integer, p_check_date date) IS 'Check if a shift is active on a specific date based on recurrence pattern';


--
-- Name: is_trip_terminal(public.trip_status); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.is_trip_terminal(p_status public.trip_status) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN p_status IN ('trip_completed', 'stopped', 'cancelled');
END;
$$;


--
-- Name: log_automated_fix(character varying, character varying, boolean, text, jsonb, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.log_automated_fix(p_module_name character varying, p_fix_type character varying, p_success boolean, p_message text, p_details jsonb DEFAULT NULL::jsonb, p_affected_records integer DEFAULT 0) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_log_id INTEGER;
BEGIN
    INSERT INTO automated_fix_logs (
        module_name, fix_type, success, message, details, affected_records
    ) VALUES (
        p_module_name, p_fix_type, p_success, p_message, p_details, p_affected_records
    ) RETURNING id INTO v_log_id;
    
    RETURN v_log_id;
END;
$$;


--
-- Name: FUNCTION log_automated_fix(p_module_name character varying, p_fix_type character varying, p_success boolean, p_message text, p_details jsonb, p_affected_records integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.log_automated_fix(p_module_name character varying, p_fix_type character varying, p_success boolean, p_message text, p_details jsonb, p_affected_records integer) IS 'Logs automated fix operations with success status and affected records';


--
-- Name: log_system_event(character varying, text, jsonb, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.log_system_event(p_log_type character varying, p_message text, p_details jsonb DEFAULT NULL::jsonb, p_user_id integer DEFAULT NULL::integer) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_log_id INTEGER;
BEGIN
    INSERT INTO system_logs (log_type, message, details, user_id)
    VALUES (p_log_type, p_message, p_details, p_user_id)
    RETURNING id INTO v_log_id;
    
    RETURN v_log_id;
END;
$$;


--
-- Name: FUNCTION log_system_event(p_log_type character varying, p_message text, p_details jsonb, p_user_id integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.log_system_event(p_log_type character varying, p_message text, p_details jsonb, p_user_id integer) IS 'Logs system events with optional details and user context';


--
-- Name: monitor_shift_system(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.monitor_shift_system() RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_health_record RECORD;
    v_issues_found INTEGER := 0;
    v_result TEXT;
BEGIN
    -- Run health check
    FOR v_health_record IN SELECT * FROM shift_system_health_check()
    LOOP
        IF NOT v_health_record.passed THEN
            v_issues_found := v_issues_found + 1;
            RAISE WARNING 'Health Check Failed: % - %', v_health_record.check_name, v_health_record.details;
        END IF;
    END LOOP;
    
    -- Auto-cleanup if issues found
    IF v_issues_found > 0 THEN
        PERFORM cleanup_shift_system();
        v_result := 'Issues detected and auto-cleanup performed';
    ELSE
        v_result := 'System healthy - no issues detected';
    END IF;
    
    RAISE NOTICE '%', v_result;
    RETURN v_result;
END;
$$;


--
-- Name: FUNCTION monitor_shift_system(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.monitor_shift_system() IS 'Continuous monitoring function with auto-cleanup capabilities';


--
-- Name: refresh_all_analytics_views(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.refresh_all_analytics_views() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  REFRESH MATERIALIZED VIEW mv_fleet_status_summary;
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'trip_logs' AND column_name = 'breakdown_reported_at'
  ) THEN
    REFRESH MATERIALIZED VIEW mv_breakdown_analytics_summary;
  END IF;
END;
$$;


--
-- Name: FUNCTION refresh_all_analytics_views(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.refresh_all_analytics_views() IS 'Refresh all analytics materialized views for optimal performance';


--
-- Name: refresh_breakdown_analytics_summary(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.refresh_breakdown_analytics_summary() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'trip_logs' AND column_name = 'breakdown_reported_at'
  ) THEN
    REFRESH MATERIALIZED VIEW mv_breakdown_analytics_summary;
  END IF;
END;
$$;


--
-- Name: refresh_fleet_status_summary(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.refresh_fleet_status_summary() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  REFRESH MATERIALIZED VIEW mv_fleet_status_summary;
END;
$$;


--
-- Name: refresh_trip_performance_summary(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.refresh_trip_performance_summary() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_trip_performance_summary;
END;
$$;


--
-- Name: schedule_auto_activation(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.schedule_auto_activation() RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
    shift_record RECORD;
    calculated_status TEXT;
    current_status TEXT;
    activated_count INTEGER := 0;
    scheduled_count INTEGER := 0;
    completed_count INTEGER := 0;
    updated_count INTEGER := 0;
BEGIN
    -- Process all non-cancelled shifts
    FOR shift_record IN 
        SELECT id, status FROM driver_shifts 
        WHERE status != 'cancelled'
    LOOP
        current_status := shift_record.status;
        calculated_status := evaluate_shift_status(shift_record.id, CURRENT_TIMESTAMP);
        
        -- Only update if status has changed and is not 'error'
        IF calculated_status != current_status AND calculated_status != 'error' THEN
            -- Update the shift status
            UPDATE driver_shifts 
            SET status = calculated_status::shift_status, 
                updated_at = CURRENT_TIMESTAMP 
            WHERE id = shift_record.id;
            
            updated_count := updated_count + 1;
            
            -- Count by status type
            CASE calculated_status
                WHEN 'active' THEN activated_count := activated_count + 1;
                WHEN 'scheduled' THEN scheduled_count := scheduled_count + 1;
                WHEN 'completed' THEN completed_count := completed_count + 1;
            END CASE;
        END IF;
    END LOOP;
    
    -- Log the results if any updates were made
    IF updated_count > 0 THEN
        INSERT INTO system_logs (
            log_type, 
            message, 
            details
        ) VALUES (
            'SHIFT_AUTO_UPDATE',
            'Auto-updated shifts based on current time',
            jsonb_build_object(
                'updated_count', updated_count,
                'activated_count', activated_count,
                'scheduled_count', scheduled_count,
                'completed_count', completed_count,
                'timestamp', CURRENT_TIMESTAMP
            )
        );
    END IF;
END;
$$;


--
-- Name: FUNCTION schedule_auto_activation(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.schedule_auto_activation() IS 'Auto-updates shifts including completion when appropriate';


--
-- Name: schedule_shift_activation(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.schedule_shift_activation() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    PERFORM auto_activate_shifts();
END;
$$;


--
-- Name: set_display_type_trigger(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.set_display_type_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$

BEGIN

    -- For auto-created shifts (QR-based), set shift_type based on start_time

    IF NEW.auto_created = true AND NEW.shift_type = 'custom' THEN

        NEW.shift_type := determine_shift_type_by_time(NEW.start_time);

    END IF;

    

    -- If display_type is not explicitly set, compute it intelligently

    IF NEW.display_type IS NULL THEN

        -- If user selected day or night explicitly, respect that

        IF NEW.shift_type IN ('day', 'night') THEN

            NEW.display_type := NEW.shift_type;

        ELSE

            -- For custom shifts, use intelligent classification

            NEW.display_type := classify_shift_by_time(NEW.start_time, NEW.end_time);

        END IF;

    END IF;

    

    RETURN NEW;

END;

$$;


--
-- Name: FUNCTION set_display_type_trigger(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.set_display_type_trigger() IS 'Enhanced trigger that sets both shift_type and display_type for QR-created shifts';


--
-- Name: shift_includes_date(date, date, date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.shift_includes_date(p_start_date date, p_end_date date, p_query_date date) RETURNS boolean
    LANGUAGE plpgsql IMMUTABLE
    AS $$
BEGIN
    RETURN p_query_date BETWEEN p_start_date AND p_end_date;
END;
$$;


--
-- Name: shift_overlaps_range(date, date, date, date); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.shift_overlaps_range(p_start_date date, p_end_date date, p_range_start date, p_range_end date) RETURNS boolean
    LANGUAGE plpgsql IMMUTABLE
    AS $$
BEGIN
    RETURN p_start_date <= p_range_end AND p_end_date >= p_range_start;
END;
$$;


--
-- Name: shift_system_health_check(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.shift_system_health_check() RETURNS TABLE(check_name text, status text, details text, passed boolean)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_function_count INTEGER;
    v_signature_issues INTEGER;
    v_status_inconsistencies INTEGER;
    v_assignment_issues INTEGER;
BEGIN
    -- Check 1: Verify all required functions exist with correct signatures
    SELECT COUNT(*) INTO v_function_count
    FROM pg_proc 
    WHERE proname IN ('evaluate_shift_status', 'schedule_auto_activation', 'update_all_shift_statuses')
    AND pg_get_function_arguments(oid) NOT LIKE '%boolean%'; -- No 3-parameter functions
    
    RETURN QUERY SELECT 
        'Function Signatures'::TEXT,
        CASE WHEN v_function_count = 3 THEN 'PASS' ELSE 'FAIL' END::TEXT,
        CASE WHEN v_function_count = 3 THEN 'All functions have correct signatures' 
             ELSE 'Missing functions or incorrect signatures detected' END::TEXT,
        v_function_count = 3;
    
    -- Check 2: Verify no problematic 3-parameter functions exist
    SELECT COUNT(*) INTO v_signature_issues
    FROM pg_proc 
    WHERE proname LIKE '%shift%' 
    AND pg_get_function_arguments(oid) LIKE '%boolean%';
    
    RETURN QUERY SELECT 
        'No Legacy Functions'::TEXT,
        CASE WHEN v_signature_issues = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT,
        CASE WHEN v_signature_issues = 0 THEN 'No problematic 3-parameter functions found'
             ELSE v_signature_issues || ' problematic functions detected' END::TEXT,
        v_signature_issues = 0;
    
    -- Check 3: Verify shift status consistency
    SELECT COUNT(*) INTO v_status_inconsistencies
    FROM driver_shifts ds
    WHERE ds.status != 'cancelled'
    AND ds.status::TEXT != evaluate_shift_status(ds.id, CURRENT_TIMESTAMP);
    
    RETURN QUERY SELECT 
        'Status Consistency'::TEXT,
        CASE WHEN v_status_inconsistencies = 0 THEN 'PASS' ELSE 'WARN' END::TEXT,
        CASE WHEN v_status_inconsistencies = 0 THEN 'All shift statuses are consistent'
             ELSE v_status_inconsistencies || ' shifts have inconsistent statuses' END::TEXT,
        v_status_inconsistencies = 0;
    
    -- Check 4: Verify assignment display logic
    SELECT COUNT(*) INTO v_assignment_issues
    FROM assignments a
    JOIN dump_trucks t ON a.truck_id = t.id
    LEFT JOIN driver_shifts ds ON (
        ds.truck_id = a.truck_id
        AND ds.status = 'active'
        AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
        AND (
            (ds.end_time < ds.start_time AND
             (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
            OR
            (ds.end_time >= ds.start_time AND
             CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
        )
    )
    WHERE ds.id IS NULL; -- No active shift found when there should be one
    
    RETURN QUERY SELECT 
        'Assignment Display'::TEXT,
        CASE WHEN v_assignment_issues = 0 THEN 'PASS' ELSE 'WARN' END::TEXT,
        CASE WHEN v_assignment_issues = 0 THEN 'All assignments showing active shifts correctly'
             ELSE v_assignment_issues || ' assignments may show "No Active Shift"' END::TEXT,
        v_assignment_issues = 0;
    
    -- Check 5: Overall system health
    RETURN QUERY SELECT 
        'Overall Health'::TEXT,
        CASE WHEN v_function_count = 3 AND v_signature_issues = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT,
        'System health based on critical checks'::TEXT,
        v_function_count = 3 AND v_signature_issues = 0;
END;
$$;


--
-- Name: FUNCTION shift_system_health_check(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.shift_system_health_check() IS 'Comprehensive health check for shift management system - prevents regression';


--
-- Name: sync_shift_date_with_start_date(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.sync_shift_date_with_start_date() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  -- Auto-sync shift_date with start_date for backward compatibility
  -- This ensures existing code that references shift_date continues to work
  NEW.shift_date = NEW.start_date;
  RETURN NEW;
END;
$$;


--
-- Name: test_shift_scenarios(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.test_shift_scenarios() RETURNS TABLE(scenario text, shift_type text, start_time time without time zone, end_time time without time zone, test_time time without time zone, expected_status text, actual_status text, passed boolean)
    LANGUAGE plpgsql
    AS $$
DECLARE
    test_shift_id INTEGER;
    test_date DATE := CURRENT_DATE;
BEGIN
    -- Test Day Shift Scenarios
    -- Scenario 1: Day shift before start
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'day', test_date, test_date, '08:00:00', '16:00:00', 'scheduled', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Day shift before start'::TEXT,
        'day'::TEXT,
        '08:00:00'::TIME,
        '16:00:00'::TIME,
        '07:00:00'::TIME,
        'scheduled'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '07:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '07:00:00'::TIME) = 'scheduled';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 2: Day shift during active period
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'day', test_date, test_date, '08:00:00', '16:00:00', 'scheduled', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Day shift during active'::TEXT,
        'day'::TEXT,
        '08:00:00'::TIME,
        '16:00:00'::TIME,
        '12:00:00'::TIME,
        'active'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '12:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '12:00:00'::TIME) = 'active';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 3: Day shift after end
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'day', test_date, test_date, '08:00:00', '16:00:00', 'active', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Day shift after end'::TEXT,
        'day'::TEXT,
        '08:00:00'::TIME,
        '16:00:00'::TIME,
        '17:00:00'::TIME,
        'completed'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '17:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '17:00:00'::TIME) = 'completed';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Test Night Shift Scenarios
    -- Scenario 4: Night shift before start (same day)
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'night', test_date, test_date, '22:00:00', '06:00:00', 'scheduled', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Night shift before start'::TEXT,
        'night'::TEXT,
        '22:00:00'::TIME,
        '06:00:00'::TIME,
        '20:00:00'::TIME,
        'scheduled'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '20:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '20:00:00'::TIME) = 'scheduled';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 5: Night shift during active (evening)
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'night', test_date, test_date, '22:00:00', '06:00:00', 'scheduled', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Night shift active evening'::TEXT,
        'night'::TEXT,
        '22:00:00'::TIME,
        '06:00:00'::TIME,
        '23:00:00'::TIME,
        'active'::TEXT,
        evaluate_shift_status(test_shift_id, test_date + '23:00:00'::TIME),
        evaluate_shift_status(test_shift_id, test_date + '23:00:00'::TIME) = 'active';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 6: Night shift during active (morning next day)
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'night', test_date, test_date, '22:00:00', '06:00:00', 'active', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Night shift active morning'::TEXT,
        'night'::TEXT,
        '22:00:00'::TIME,
        '06:00:00'::TIME,
        '03:00:00'::TIME,
        'active'::TEXT,
        evaluate_shift_status(test_shift_id, (test_date + INTERVAL '1 day') + '03:00:00'::TIME),
        evaluate_shift_status(test_shift_id, (test_date + INTERVAL '1 day') + '03:00:00'::TIME) = 'active';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
    
    -- Scenario 7: Night shift completed (next day after end)
    INSERT INTO driver_shifts (truck_id, driver_id, shift_type, start_date, end_date, start_time, end_time, status, recurrence_pattern)
    VALUES (1, 1, 'night', test_date, test_date, '22:00:00', '06:00:00', 'active', 'single')
    RETURNING id INTO test_shift_id;
    
    RETURN QUERY SELECT 
        'Night shift completed'::TEXT,
        'night'::TEXT,
        '22:00:00'::TIME,
        '06:00:00'::TIME,
        '07:00:00'::TIME,
        'completed'::TEXT,
        evaluate_shift_status(test_shift_id, (test_date + INTERVAL '1 day') + '07:00:00'::TIME),
        evaluate_shift_status(test_shift_id, (test_date + INTERVAL '1 day') + '07:00:00'::TIME) = 'completed';
    
    DELETE FROM driver_shifts WHERE id = test_shift_id;
END;
$$;


--
-- Name: FUNCTION test_shift_scenarios(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.test_shift_scenarios() IS 'Comprehensive testing function for day and night shift logic validation';


--
-- Name: test_shift_time_logic(time without time zone, time without time zone, time without time zone, boolean); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.test_shift_time_logic(p_start_time time without time zone, p_end_time time without time zone, p_test_time time without time zone, p_is_overnight boolean DEFAULT NULL::boolean) RETURNS TABLE(is_overnight boolean, is_within_window boolean, logic_used text)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_is_overnight BOOLEAN;
    v_is_within_window BOOLEAN;
    v_logic_used TEXT;
BEGIN
    -- Determine if overnight (or use provided value for testing)
    v_is_overnight := COALESCE(p_is_overnight, p_end_time < p_start_time);

    IF v_is_overnight THEN
        -- Night shift: Use dual condition logic
        v_is_within_window := (p_test_time >= p_start_time OR p_test_time <= p_end_time);
        v_logic_used := 'dual_condition_overnight';
    ELSE
        -- Day shift: Use simple BETWEEN logic
        v_is_within_window := (p_test_time BETWEEN p_start_time AND p_end_time);
        v_logic_used := 'simple_between_day';
    END IF;

    RETURN QUERY SELECT v_is_overnight, v_is_within_window, v_logic_used;
END;
$$;


--
-- Name: FUNCTION test_shift_time_logic(p_start_time time without time zone, p_end_time time without time zone, p_test_time time without time zone, p_is_overnight boolean); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.test_shift_time_logic(p_start_time time without time zone, p_end_time time without time zone, p_test_time time without time zone, p_is_overnight boolean) IS 'Testing function to validate shift time logic for day and night shifts';


--
-- Name: update_all_shift_statuses(timestamp with time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_all_shift_statuses(p_reference_timestamp timestamp with time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(updated_count integer, activated_count integer, scheduled_count integer, completed_count integer, total_count integer)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_updated_count INTEGER := 0;
    v_activated_count INTEGER := 0;
    v_scheduled_count INTEGER := 0;
    v_completed_count INTEGER := 0;
    v_total_count INTEGER := 0;
    v_shift RECORD;
    v_calculated_status TEXT;
    v_existing_active_shift_id INTEGER;
    v_existing_auto_created BOOLEAN;
    v_current_auto_created BOOLEAN;
BEGIN
    -- Count total non-cancelled shifts
    SELECT COUNT(*) INTO v_total_count FROM driver_shifts WHERE status != 'cancelled';
    
    -- Process all non-cancelled shifts with conflict resolution
    FOR v_shift IN 
        SELECT id, driver_id, status::TEXT, auto_created FROM driver_shifts 
        WHERE status != 'cancelled'
        ORDER BY auto_created DESC, created_at DESC  -- Prioritize auto_created shifts
    LOOP
        -- Calculate the correct status
        v_calculated_status := evaluate_shift_status(v_shift.id, p_reference_timestamp);
        
        -- Only update if status has changed and is not 'error'
        IF v_calculated_status != v_shift.status AND v_calculated_status != 'error' THEN
            
            -- Special handling for activations to prevent constraint violations
            IF v_calculated_status = 'active' THEN
                -- Check if there's already an active shift for this driver
                SELECT id, auto_created INTO v_existing_active_shift_id, v_existing_auto_created
                FROM driver_shifts 
                WHERE driver_id = v_shift.driver_id 
                  AND status = 'active' 
                  AND id != v_shift.id;
                
                -- If there's a conflict, apply precedence rules
                IF v_existing_active_shift_id IS NOT NULL THEN
                    v_current_auto_created := v_shift.auto_created;
                    
                    -- Auto-created shifts take precedence over scheduled shifts
                    IF v_current_auto_created AND NOT v_existing_auto_created THEN
                        -- Deactivate the existing scheduled shift
                        UPDATE driver_shifts 
                        SET status = 'scheduled', 
                            updated_at = p_reference_timestamp
                        WHERE id = v_existing_active_shift_id;
                        
                        -- Activate the current auto-created shift
                        UPDATE driver_shifts 
                        SET status = 'active', 
                            updated_at = p_reference_timestamp
                        WHERE id = v_shift.id;
                        
                        v_updated_count := v_updated_count + 2;
                        v_activated_count := v_activated_count + 1;
                        v_scheduled_count := v_scheduled_count + 1;
                        
                    ELSIF NOT v_current_auto_created AND v_existing_auto_created THEN
                        -- Keep the existing auto-created shift active, don't activate scheduled shift
                        -- No update needed, skip this shift
                        CONTINUE;
                        
                    ELSE
                        -- Both are same type (both auto_created or both scheduled)
                        -- Keep the newer one (higher ID), deactivate the older one
                        IF v_shift.id > v_existing_active_shift_id THEN
                            UPDATE driver_shifts 
                            SET status = 'scheduled', 
                                updated_at = p_reference_timestamp
                            WHERE id = v_existing_active_shift_id;
                            
                            UPDATE driver_shifts 
                            SET status = 'active', 
                                updated_at = p_reference_timestamp
                            WHERE id = v_shift.id;
                            
                            v_updated_count := v_updated_count + 2;
                            v_activated_count := v_activated_count + 1;
                            v_scheduled_count := v_scheduled_count + 1;
                        ELSE
                            -- Keep the existing newer shift, don't activate this older one
                            CONTINUE;
                        END IF;
                    END IF;
                ELSE
                    -- No conflict, safe to activate
                    UPDATE driver_shifts 
                    SET status = v_calculated_status::shift_status, 
                        updated_at = p_reference_timestamp
                    WHERE id = v_shift.id;
                    
                    v_updated_count := v_updated_count + 1;
                    v_activated_count := v_activated_count + 1;
                END IF;
            ELSE
                -- Non-activation updates (scheduled, completed) - safe to update
                UPDATE driver_shifts 
                SET status = v_calculated_status::shift_status, 
                    updated_at = p_reference_timestamp
                WHERE id = v_shift.id;
                
                v_updated_count := v_updated_count + 1;
                
                -- Count by status type
                CASE v_calculated_status
                    WHEN 'scheduled' THEN v_scheduled_count := v_scheduled_count + 1;
                    WHEN 'completed' THEN v_completed_count := v_completed_count + 1;
                END CASE;
            END IF;
        END IF;
    END LOOP;
    
    -- Return the statistics
    updated_count := v_updated_count;
    activated_count := v_activated_count;
    scheduled_count := v_scheduled_count;
    completed_count := v_completed_count;
    total_count := v_total_count;
    
    RETURN NEXT;
END;
$$;


--
-- Name: FUNCTION update_all_shift_statuses(p_reference_timestamp timestamp with time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.update_all_shift_statuses(p_reference_timestamp timestamp with time zone) IS 'Updates shift statuses with conflict resolution - auto_created shifts take precedence over scheduled shifts';


--
-- Name: update_assignment_on_trip_complete(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_assignment_on_trip_complete() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_completed_trips INTEGER;
    v_expected_loads INTEGER;
BEGIN
    -- Only process if trip is completed
    IF NEW.status = 'trip_completed' AND OLD.status != 'trip_completed' THEN
        -- Count completed trips for this assignment today
        SELECT COUNT(*), MAX(a.expected_loads_per_day)
        INTO v_completed_trips, v_expected_loads
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE tl.assignment_id = NEW.assignment_id
          AND tl.status = 'trip_completed'
          AND DATE(tl.created_at) = CURRENT_DATE
        GROUP BY a.expected_loads_per_day;

        -- Update assignment status if all expected loads completed
        IF v_completed_trips >= v_expected_loads THEN
            UPDATE assignments
            SET status = 'completed',
                end_time = NEW.trip_completed_time,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = NEW.assignment_id;
        END IF;
    END IF;

    RETURN NEW;
END;
$$;


--
-- Name: update_shift_status(integer, timestamp without time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_shift_status(p_shift_id integer, p_reference_timestamp timestamp without time zone DEFAULT CURRENT_TIMESTAMP) RETURNS TABLE(old_status text, new_status text, status_changed boolean, truck_id integer, driver_id integer)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_new_status TEXT;
    v_old_status TEXT;
    v_truck_id INTEGER;
    v_driver_id INTEGER;
    v_shift_info RECORD;
BEGIN
    -- Get current shift information
    SELECT status, truck_id, driver_id, shift_type
    INTO v_shift_info
    FROM driver_shifts
    WHERE id = p_shift_id;

    IF NOT FOUND THEN
        RETURN QUERY SELECT 'shift_not_found'::TEXT, 'error'::TEXT, FALSE, NULL::INTEGER, NULL::INTEGER;
        RETURN;
    END IF;

    v_old_status := v_shift_info.status;
    v_truck_id := v_shift_info.truck_id;
    v_driver_id := v_shift_info.driver_id;

    -- Evaluate new status using enhanced logic
    v_new_status := evaluate_shift_status(p_shift_id, p_reference_timestamp);

    -- Update if status has changed and new status is valid
    IF v_new_status != v_old_status AND v_new_status != 'error' THEN
        UPDATE driver_shifts
        SET status = v_new_status::shift_status,
            updated_at = p_reference_timestamp
        WHERE id = p_shift_id;

        -- Log the transition for monitoring
        RAISE NOTICE 'Shift status updated: ID=%, Truck=%, Driver=%, %->%',
            p_shift_id, v_truck_id, v_driver_id, v_old_status, v_new_status;

        RETURN QUERY SELECT v_old_status, v_new_status, TRUE, v_truck_id, v_driver_id;
    ELSE
        RETURN QUERY SELECT v_old_status, v_old_status, FALSE, v_truck_id, v_driver_id;
    END IF;
END;
$$;


--
-- Name: FUNCTION update_shift_status(p_shift_id integer, p_reference_timestamp timestamp without time zone); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.update_shift_status(p_shift_id integer, p_reference_timestamp timestamp without time zone) IS 'Updates status for a specific shift with enhanced validation and logging';


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


--
-- Name: validate_all_shift_statuses(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.validate_all_shift_statuses() RETURNS TABLE(shift_id integer, truck_id integer, driver_id integer, shift_type text, current_status text, calculated_status text, needs_update boolean, is_overnight boolean)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ds.id,
        ds.truck_id,
        ds.driver_id,
        ds.shift_type,
        ds.status::TEXT,
        evaluate_shift_status(ds.id, CURRENT_TIMESTAMP),
        (ds.status::TEXT != evaluate_shift_status(ds.id, CURRENT_TIMESTAMP)),
        (ds.end_time < ds.start_time)
    FROM driver_shifts ds
    WHERE ds.status != 'cancelled'
    ORDER BY ds.truck_id, ds.start_time;
END;
$$;


--
-- Name: FUNCTION validate_all_shift_statuses(); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.validate_all_shift_statuses() IS 'Validates all current shift statuses against calculated statuses';


--
-- Name: validate_driver_qr_code(jsonb); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.validate_driver_qr_code(qr_data jsonb) RETURNS boolean
    LANGUAGE plpgsql
    AS $_$

DECLARE

    required_fields TEXT[] := ARRAY['id', 'driver_id', 'employee_id', 'generated_date'];

    field TEXT;

BEGIN

    -- Check if qr_data is not null

    IF qr_data IS NULL THEN

        RETURN FALSE;

    END IF;

    

    -- Check if all required fields exist

    FOREACH field IN ARRAY required_fields

    LOOP

        IF NOT (qr_data ? field) THEN

            RETURN FALSE;

        END IF;

    END LOOP;

    

    -- Validate field types and values

    IF NOT (

        (qr_data->>'driver_id')::TEXT ~ '^\d+$' AND

        (qr_data->>'employee_id')::TEXT != '' AND

        (qr_data->>'id')::TEXT != '' AND

        (qr_data->>'generated_date')::TEXT ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}'

    ) THEN

        RETURN FALSE;

    END IF;

    

    RETURN TRUE;

END;

$_$;


--
-- Name: FUNCTION validate_driver_qr_code(qr_data jsonb); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.validate_driver_qr_code(qr_data jsonb) IS 'Validates the structure and content of driver QR code data';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: approvals; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.approvals (
    id integer NOT NULL,
    trip_log_id integer NOT NULL,
    exception_type character varying(50) NOT NULL,
    exception_description text NOT NULL,
    severity character varying(20) DEFAULT 'medium'::character varying,
    reported_by integer,
    requested_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    reviewed_by integer,
    reviewed_at timestamp without time zone,
    status public.approval_status DEFAULT 'pending'::public.approval_status NOT NULL,
    notes text,
    is_adaptive_exception boolean DEFAULT false,
    adaptation_strategy character varying(50),
    adaptation_confidence character varying(20),
    auto_approved boolean DEFAULT false,
    adaptation_metadata jsonb,
    suggested_assignment_id integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    reason text,
    CONSTRAINT approvals_severity_check CHECK (((severity)::text = ANY (ARRAY[('low'::character varying)::text, ('medium'::character varying)::text, ('high'::character varying)::text, ('critical'::character varying)::text]))),
    CONSTRAINT chk_approvals_adaptation_confidence CHECK (((adaptation_confidence IS NULL) OR ((adaptation_confidence)::text = ANY (ARRAY[('high'::character varying)::text, ('medium'::character varying)::text, ('low'::character varying)::text])))),
    CONSTRAINT chk_approvals_adaptation_strategy CHECK (((adaptation_strategy IS NULL) OR ((adaptation_strategy)::text = ANY (ARRAY[('pattern_based'::character varying)::text, ('proximity_based'::character varying)::text, ('efficiency_based'::character varying)::text, ('manual_override'::character varying)::text]))))
);


--
-- Name: approvals_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.approvals_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: approvals_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.approvals_id_seq OWNED BY public.approvals.id;


--
-- Name: assignments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.assignments (
    id integer NOT NULL,
    assignment_code character varying(50),
    truck_id integer NOT NULL,
    driver_id integer,
    loading_location_id integer NOT NULL,
    unloading_location_id integer NOT NULL,
    status public.assignment_status DEFAULT 'assigned'::public.assignment_status NOT NULL,
    priority character varying(20) DEFAULT 'normal'::character varying,
    assigned_date date,
    start_time timestamp without time zone,
    end_time timestamp without time zone,
    expected_loads_per_day integer DEFAULT 1,
    is_adaptive boolean DEFAULT false,
    adaptation_strategy character varying(50),
    adaptation_confidence character varying(20),
    adaptation_metadata jsonb,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    shift_id integer,
    is_shift_assignment boolean DEFAULT false,
    shift_handover_id integer,
    auto_created boolean DEFAULT false,
    CONSTRAINT assignments_priority_check CHECK (((priority)::text = ANY (ARRAY[('low'::character varying)::text, ('normal'::character varying)::text, ('high'::character varying)::text, ('urgent'::character varying)::text]))),
    CONSTRAINT chk_assignment_integrity CHECK (((driver_id IS NOT NULL) OR ((notes IS NOT NULL) AND ((notes ~~ '%Auto-assigned%'::text) OR (notes ~~ '%No active driver%'::text) OR (notes ~~ '%manual assignment required%'::text))) OR ((assignment_code IS NOT NULL) AND ((assignment_code)::text ~~ '%AUTO%'::text)))),
    CONSTRAINT chk_assignments_adaptation_confidence CHECK (((adaptation_confidence IS NULL) OR ((adaptation_confidence)::text = ANY (ARRAY[('high'::character varying)::text, ('medium'::character varying)::text, ('low'::character varying)::text])))),
    CONSTRAINT chk_assignments_adaptation_strategy CHECK (((adaptation_strategy IS NULL) OR ((adaptation_strategy)::text = ANY (ARRAY[('pattern_based'::character varying)::text, ('proximity_based'::character varying)::text, ('efficiency_based'::character varying)::text, ('manual_override'::character varying)::text]))))
);


--
-- Name: COLUMN assignments.assigned_date; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.assignments.assigned_date IS 'Planned date for the assignment, can be null for flexible assignments';


--
-- Name: assignments_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.assignments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: assignments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.assignments_id_seq OWNED BY public.assignments.id;


--
-- Name: automated_fix_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.automated_fix_logs (
    id integer NOT NULL,
    module_name character varying(50) NOT NULL,
    fix_type character varying(50) DEFAULT 'automated_fix'::character varying NOT NULL,
    success boolean DEFAULT false NOT NULL,
    message text NOT NULL,
    details jsonb,
    affected_records integer DEFAULT 0,
    executed_at timestamp with time zone DEFAULT now() NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE automated_fix_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.automated_fix_logs IS 'Audit log for automated system health fixes';


--
-- Name: COLUMN automated_fix_logs.module_name; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.module_name IS 'Module that was fixed (shift_management, assignment_management, trip_monitoring)';


--
-- Name: COLUMN automated_fix_logs.fix_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.fix_type IS 'Type of fix operation performed';


--
-- Name: COLUMN automated_fix_logs.success; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.success IS 'Whether the fix operation completed successfully';


--
-- Name: COLUMN automated_fix_logs.message; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.message IS 'Human-readable summary of the fix operation';


--
-- Name: COLUMN automated_fix_logs.details; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.details IS 'Detailed information about the fix operation in JSON format';


--
-- Name: COLUMN automated_fix_logs.affected_records; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.affected_records IS 'Number of database records affected by the fix';


--
-- Name: COLUMN automated_fix_logs.executed_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.automated_fix_logs.executed_at IS 'When the fix operation was executed';


--
-- Name: automated_fix_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.automated_fix_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: automated_fix_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.automated_fix_logs_id_seq OWNED BY public.automated_fix_logs.id;


--
-- Name: driver_shifts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_shifts (
    id integer NOT NULL,
    truck_id integer NOT NULL,
    driver_id integer NOT NULL,
    shift_type public.shift_type DEFAULT 'day'::public.shift_type NOT NULL,
    start_time time without time zone NOT NULL,
    end_time time without time zone,
    status public.shift_status DEFAULT 'scheduled'::public.shift_status NOT NULL,
    previous_shift_id integer,
    handover_notes text,
    handover_completed_at timestamp without time zone,
    assignment_id integer,
    auto_created boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    start_date date NOT NULL,
    end_date date,
    recurrence_pattern public.recurrence_pattern DEFAULT 'single'::public.recurrence_pattern NOT NULL,
    display_type public.shift_type,
    shift_date date,
    completion_notes text,
    cancellation_reason text,
    security_context jsonb,
    audit_trail jsonb DEFAULT '[]'::jsonb,
    CONSTRAINT active_shift_null_end_fields CHECK ((((status = 'active'::public.shift_status) AND (auto_created = true)) OR ((status <> 'active'::public.shift_status) OR (auto_created = false) OR ((end_date IS NOT NULL) AND (end_time IS NOT NULL))))),
    CONSTRAINT check_valid_datetime_combination CHECK (((start_date + start_time) IS NOT NULL)),
    CONSTRAINT valid_unified_date_range_flexible CHECK (((start_date IS NULL) OR ((start_date IS NOT NULL) AND ((end_date IS NULL) OR ((end_date IS NOT NULL) AND (start_date <= end_date))))))
);


--
-- Name: COLUMN driver_shifts.end_time; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_shifts.end_time IS 'NULL for active QR-created shifts, populated when driver checks out';


--
-- Name: COLUMN driver_shifts.start_date; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_shifts.start_date IS 'Start date of shift range. For single-day shifts, same as end_date.';


--
-- Name: COLUMN driver_shifts.end_date; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_shifts.end_date IS 'NULL for active QR-created shifts, populated when driver checks out';


--
-- Name: COLUMN driver_shifts.recurrence_pattern; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_shifts.recurrence_pattern IS 'Recurrence pattern: single, daily, weekly, weekdays, weekends, custom';


--
-- Name: COLUMN driver_shifts.display_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_shifts.display_type IS 'Computed display type for UI (may differ from shift_type for intelligent classification)';


--
-- Name: COLUMN driver_shifts.shift_date; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_shifts.shift_date IS 'Optional backward compatibility column. Auto-synced with start_date via trigger. Maintained for legacy code compatibility while unified approach uses start_date/end_date.';


--
-- Name: CONSTRAINT check_valid_datetime_combination ON driver_shifts; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON CONSTRAINT check_valid_datetime_combination ON public.driver_shifts IS 'Ensures start_date and start_time can be combined into a valid timestamp';


--
-- Name: driver_shifts_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.driver_shifts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: driver_shifts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.driver_shifts_id_seq OWNED BY public.driver_shifts.id;


--
-- Name: driver_status_audit; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.driver_status_audit (
    id integer NOT NULL,
    driver_id integer NOT NULL,
    employee_id character varying(20) NOT NULL,
    driver_status character varying(20) NOT NULL,
    operation_attempted character varying(50) NOT NULL,
    blocked_at timestamp without time zone DEFAULT now() NOT NULL,
    ip_address character varying(45),
    user_agent text,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


--
-- Name: TABLE driver_status_audit; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.driver_status_audit IS 'Audit trail for blocked driver operations due to inactive, suspended, on_leave, or terminated status';


--
-- Name: COLUMN driver_status_audit.driver_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_status_audit.driver_id IS 'Foreign key reference to drivers table';


--
-- Name: COLUMN driver_status_audit.employee_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_status_audit.employee_id IS 'Driver employee ID for quick reference';


--
-- Name: COLUMN driver_status_audit.driver_status; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_status_audit.driver_status IS 'Driver status at time of blocked operation (inactive, suspended, on_leave, terminated)';


--
-- Name: COLUMN driver_status_audit.operation_attempted; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_status_audit.operation_attempted IS 'Type of operation that was blocked (check_in, check_out, qr_scan, etc.)';


--
-- Name: COLUMN driver_status_audit.blocked_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_status_audit.blocked_at IS 'Timestamp when the operation was blocked';


--
-- Name: COLUMN driver_status_audit.ip_address; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_status_audit.ip_address IS 'IP address of the request (for security monitoring)';


--
-- Name: COLUMN driver_status_audit.user_agent; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.driver_status_audit.user_agent IS 'User agent string from the request';


--
-- Name: driver_status_audit_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.driver_status_audit_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: driver_status_audit_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.driver_status_audit_id_seq OWNED BY public.driver_status_audit.id;


--
-- Name: drivers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.drivers (
    id integer NOT NULL,
    employee_id character varying(20) NOT NULL,
    full_name character varying(100) NOT NULL,
    license_number character varying(30) NOT NULL,
    license_expiry date NOT NULL,
    phone character varying(20),
    email character varying(100),
    address text,
    hire_date date NOT NULL,
    status public.driver_status DEFAULT 'active'::public.driver_status NOT NULL,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    driver_qr_code jsonb
);


--
-- Name: COLUMN drivers.driver_qr_code; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.drivers.driver_qr_code IS 'JSONB field storing driver QR code data with structure: {id, driver_id, employee_id, generated_date}';


--
-- Name: driver_status_audit_summary; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.driver_status_audit_summary AS
 SELECT dsa.id,
    dsa.employee_id,
    d.full_name,
    dsa.driver_status,
    dsa.operation_attempted,
    dsa.blocked_at,
    dsa.ip_address,
    count(*) OVER (PARTITION BY dsa.driver_id, (date(dsa.blocked_at))) AS daily_attempts,
    count(*) OVER (PARTITION BY dsa.driver_id) AS total_attempts
   FROM (public.driver_status_audit dsa
     JOIN public.drivers d ON ((dsa.driver_id = d.id)))
  ORDER BY dsa.blocked_at DESC;


--
-- Name: VIEW driver_status_audit_summary; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.driver_status_audit_summary IS 'Summary view of blocked driver operations with attempt counts for security monitoring';


--
-- Name: drivers_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.drivers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: drivers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.drivers_id_seq OWNED BY public.drivers.id;


--
-- Name: dump_trucks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.dump_trucks (
    id integer NOT NULL,
    truck_number character varying(20) NOT NULL,
    license_plate character varying(20) NOT NULL,
    make character varying(50),
    model character varying(50),
    year integer,
    capacity_tons numeric(5,2),
    qr_code_data jsonb NOT NULL,
    status public.truck_status DEFAULT 'active'::public.truck_status NOT NULL,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


--
-- Name: dump_trucks_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.dump_trucks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: dump_trucks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.dump_trucks_id_seq OWNED BY public.dump_trucks.id;


--
-- Name: health_check_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.health_check_logs (
    id integer NOT NULL,
    check_name character varying(100) NOT NULL,
    check_type character varying(50) NOT NULL,
    status character varying(20) NOT NULL,
    message text,
    details jsonb,
    execution_time_ms integer,
    checked_at timestamp with time zone DEFAULT now(),
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE health_check_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.health_check_logs IS 'System health check results and monitoring';


--
-- Name: health_check_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.health_check_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: health_check_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.health_check_logs_id_seq OWNED BY public.health_check_logs.id;


--
-- Name: locations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.locations (
    id integer NOT NULL,
    location_code character varying(20) NOT NULL,
    name character varying(100) NOT NULL,
    type public.location_type NOT NULL,
    address text,
    coordinates character varying(50),
    qr_code_data jsonb NOT NULL,
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_active boolean GENERATED ALWAYS AS (((status)::text = 'active'::text)) STORED
);


--
-- Name: locations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.locations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: locations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.locations_id_seq OWNED BY public.locations.id;


--
-- Name: migration_log; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.migration_log (
    id integer NOT NULL,
    migration_name character varying(255) NOT NULL,
    executed_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    description text
);


--
-- Name: migration_log_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.migration_log_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: migration_log_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.migration_log_id_seq OWNED BY public.migration_log.id;


--
-- Name: migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.migrations (
    id integer NOT NULL,
    filename character varying(255) NOT NULL,
    executed_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.migrations_id_seq OWNED BY public.migrations.id;


--
-- Name: trip_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.trip_logs (
    id integer NOT NULL,
    assignment_id integer NOT NULL,
    trip_number integer NOT NULL,
    status public.trip_status DEFAULT 'assigned'::public.trip_status NOT NULL,
    loading_start_time timestamp without time zone,
    loading_end_time timestamp without time zone,
    unloading_start_time timestamp without time zone,
    unloading_end_time timestamp without time zone,
    trip_completed_time timestamp without time zone,
    actual_loading_location_id integer,
    actual_unloading_location_id integer,
    is_exception boolean DEFAULT false NOT NULL,
    exception_reason text,
    exception_approved_by integer,
    exception_approved_at timestamp without time zone,
    total_duration_minutes integer,
    loading_duration_minutes integer,
    travel_duration_minutes integer,
    unloading_duration_minutes integer,
    notes jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    location_sequence jsonb,
    is_extended_trip boolean DEFAULT false,
    workflow_type character varying(50) DEFAULT 'standard'::character varying,
    baseline_trip_id integer,
    cycle_number integer DEFAULT 1,
    performed_by_driver_id integer,
    performed_by_driver_name character varying(100),
    performed_by_employee_id character varying(20),
    performed_by_shift_id integer,
    performed_by_shift_type public.shift_type,
    stopped_reported_at timestamp without time zone,
    stopped_reason text,
    stopped_resolved_at timestamp without time zone,
    stopped_resolved_by integer,
    previous_status public.trip_status,
    CONSTRAINT chk_cycle_number CHECK ((cycle_number >= 1)),
    CONSTRAINT chk_duration_non_negative CHECK ((((total_duration_minutes IS NULL) OR (total_duration_minutes >= 0)) AND ((loading_duration_minutes IS NULL) OR (loading_duration_minutes >= 0)) AND ((travel_duration_minutes IS NULL) OR (travel_duration_minutes >= 0)) AND ((unloading_duration_minutes IS NULL) OR (unloading_duration_minutes >= 0)))),
    CONSTRAINT chk_trip_timing_sequence CHECK ((((loading_start_time IS NULL) OR (loading_end_time IS NULL) OR (loading_end_time >= loading_start_time)) AND ((loading_end_time IS NULL) OR (unloading_start_time IS NULL) OR (unloading_start_time >= loading_end_time)) AND ((unloading_start_time IS NULL) OR (unloading_end_time IS NULL) OR (unloading_end_time >= unloading_start_time)) AND ((unloading_end_time IS NULL) OR (trip_completed_time IS NULL) OR (trip_completed_time >= unloading_end_time)))),
    CONSTRAINT chk_workflow_type CHECK (((workflow_type)::text = ANY (ARRAY[('standard'::character varying)::text, ('extended'::character varying)::text, ('cycle'::character varying)::text, ('dynamic'::character varying)::text])))
);


--
-- Name: COLUMN trip_logs.location_sequence; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.location_sequence IS 'JSONB array storing complete route sequence with confirmation status';


--
-- Name: COLUMN trip_logs.is_extended_trip; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.is_extended_trip IS 'Boolean flag indicating if this trip is part of an extended workflow';


--
-- Name: COLUMN trip_logs.workflow_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.workflow_type IS 'Type of workflow: standard, extended, cycle, or dynamic';


--
-- Name: COLUMN trip_logs.baseline_trip_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.baseline_trip_id IS 'Reference to original A→B trip for extended workflows';


--
-- Name: COLUMN trip_logs.cycle_number; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.cycle_number IS 'Sequential number for cycle trips (1-based)';


--
-- Name: COLUMN trip_logs.performed_by_driver_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.performed_by_driver_id IS 'ID of driver who actually performed this trip';


--
-- Name: COLUMN trip_logs.performed_by_driver_name; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.performed_by_driver_name IS 'Name of driver who performed this trip (for historical accuracy)';


--
-- Name: COLUMN trip_logs.performed_by_employee_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.performed_by_employee_id IS 'Employee ID of driver who performed this trip';


--
-- Name: COLUMN trip_logs.performed_by_shift_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.performed_by_shift_id IS 'Shift ID during which this trip was performed';


--
-- Name: COLUMN trip_logs.performed_by_shift_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.performed_by_shift_type IS 'Type of shift (day/night) during trip execution';


--
-- Name: COLUMN trip_logs.stopped_reported_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.stopped_reported_at IS 'Timestamp when the trip was stopped/reported as having issues';


--
-- Name: COLUMN trip_logs.stopped_reason; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.stopped_reason IS 'Reason why the trip was stopped (mechanical issue, accident, etc.)';


--
-- Name: COLUMN trip_logs.stopped_resolved_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.stopped_resolved_at IS 'Timestamp when the stopped trip was resolved';


--
-- Name: COLUMN trip_logs.stopped_resolved_by; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.trip_logs.stopped_resolved_by IS 'User ID who resolved the stopped trip';


--
-- Name: mv_fleet_performance_summary; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.mv_fleet_performance_summary AS
 SELECT dt.truck_number,
    dt.id AS truck_id,
    d.full_name AS driver_name,
    ll.name AS loading_location,
    ul.name AS unloading_location,
        CASE
            WHEN ((tl.status = 'loading_start'::public.trip_status) AND (tl.loading_start_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.loading_start_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'loading_end'::public.trip_status) AND (tl.loading_end_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.loading_end_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'unloading_start'::public.trip_status) AND (tl.unloading_start_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.unloading_start_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'unloading_end'::public.trip_status) AND (tl.unloading_end_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.unloading_end_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'stopped'::public.trip_status) AND (tl.stopped_reported_at IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.stopped_reported_at)::timestamp with time zone)) / (60)::numeric)
            ELSE (0)::numeric
        END AS time_in_current_phase_minutes,
    tl.total_duration_minutes,
    tl.is_exception,
    tl.stopped_reason,
    a.priority,
    a.is_adaptive,
    now() AS last_updated
   FROM (((((public.trip_logs tl
     JOIN public.assignments a ON ((tl.assignment_id = a.id)))
     JOIN public.dump_trucks dt ON ((a.truck_id = dt.id)))
     LEFT JOIN public.drivers d ON ((a.driver_id = d.id)))
     LEFT JOIN public.locations ll ON ((a.loading_location_id = ll.id)))
     LEFT JOIN public.locations ul ON ((a.unloading_location_id = ul.id)))
  WHERE (tl.created_at >= (CURRENT_DATE - '7 days'::interval))
  ORDER BY tl.created_at DESC
  WITH NO DATA;


--
-- Name: MATERIALIZED VIEW mv_fleet_performance_summary; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON MATERIALIZED VIEW public.mv_fleet_performance_summary IS 'Fleet performance metrics with stopped terminology';


--
-- Name: mv_fleet_status_summary; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.mv_fleet_status_summary AS
 SELECT dt.id AS truck_id,
    dt.truck_number,
    dt.status AS truck_status,
    d.full_name AS driver_name,
    a.assignment_code,
    a.status AS assignment_status,
    tl.status AS current_trip_status,
    tl.trip_number,
    tl.created_at AS trip_started_at,
    COALESCE(al.name, ll.name) AS current_loading_location,
    COALESCE(aul.name, ul.name) AS current_unloading_location,
        CASE
            WHEN ((tl.status = 'loading_start'::public.trip_status) AND (tl.loading_start_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.loading_start_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'loading_end'::public.trip_status) AND (tl.loading_end_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.loading_end_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'unloading_start'::public.trip_status) AND (tl.unloading_start_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.unloading_start_time)::timestamp with time zone)) / (60)::numeric)
            WHEN ((tl.status = 'unloading_end'::public.trip_status) AND (tl.unloading_end_time IS NOT NULL)) THEN (EXTRACT(epoch FROM (now() - (tl.unloading_end_time)::timestamp with time zone)) / (60)::numeric)
            ELSE (0)::numeric
        END AS time_in_current_phase_minutes,
    tl.total_duration_minutes,
    tl.is_exception,
    a.priority,
    a.is_adaptive,
    now() AS last_updated
   FROM (((((((public.dump_trucks dt
     LEFT JOIN public.assignments a ON (((dt.id = a.truck_id) AND (a.status = ANY (ARRAY['assigned'::public.assignment_status, 'in_progress'::public.assignment_status])))))
     LEFT JOIN public.drivers d ON ((a.driver_id = d.id)))
     LEFT JOIN public.trip_logs tl ON (((a.id = tl.assignment_id) AND (tl.id = ( SELECT tl2.id
           FROM public.trip_logs tl2
          WHERE (tl2.assignment_id = a.id)
          ORDER BY tl2.created_at DESC
         LIMIT 1)))))
     LEFT JOIN public.locations ll ON ((a.loading_location_id = ll.id)))
     LEFT JOIN public.locations ul ON ((a.unloading_location_id = ul.id)))
     LEFT JOIN public.locations al ON ((tl.actual_loading_location_id = al.id)))
     LEFT JOIN public.locations aul ON ((tl.actual_unloading_location_id = aul.id)))
  WHERE (dt.status = 'active'::public.truck_status)
  WITH NO DATA;


--
-- Name: MATERIALIZED VIEW mv_fleet_status_summary; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON MATERIALIZED VIEW public.mv_fleet_status_summary IS 'Real-time fleet status for Analytics & Reports dashboard';


--
-- Name: mv_stopped_analytics_summary; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.mv_stopped_analytics_summary AS
 SELECT dt.truck_number,
    dt.id AS truck_id,
    d.full_name AS driver_name,
    count(*) AS total_stopped,
    avg((EXTRACT(epoch FROM (tl.stopped_resolved_at - tl.stopped_reported_at)) / (60)::numeric)) AS avg_resolution_time_minutes,
    max(tl.stopped_reported_at) AS last_stopped_date,
    count(
        CASE
            WHEN (tl.previous_status = 'loading_start'::public.trip_status) THEN 1
            ELSE NULL::integer
        END) AS loading_phase_stopped,
    count(
        CASE
            WHEN (tl.previous_status = 'loading_end'::public.trip_status) THEN 1
            ELSE NULL::integer
        END) AS travel_to_unload_stopped,
    count(
        CASE
            WHEN (tl.previous_status = 'unloading_start'::public.trip_status) THEN 1
            ELSE NULL::integer
        END) AS unloading_phase_stopped,
    count(
        CASE
            WHEN (tl.previous_status = 'unloading_end'::public.trip_status) THEN 1
            ELSE NULL::integer
        END) AS travel_to_load_stopped,
    mode() WITHIN GROUP (ORDER BY tl.stopped_reason) AS most_common_reason,
    count(DISTINCT tl.stopped_reason) AS unique_stopped_reasons,
    mode() WITHIN GROUP (ORDER BY (EXTRACT(hour FROM tl.stopped_reported_at))) AS most_common_hour,
    mode() WITHIN GROUP (ORDER BY (EXTRACT(dow FROM tl.stopped_reported_at))) AS most_common_day_of_week,
    avg(tl.total_duration_minutes) AS avg_trip_duration_with_stopped,
    now() AS last_updated
   FROM (((public.trip_logs tl
     JOIN public.assignments a ON ((tl.assignment_id = a.id)))
     JOIN public.dump_trucks dt ON ((a.truck_id = dt.id)))
     LEFT JOIN public.drivers d ON ((a.driver_id = d.id)))
  WHERE ((tl.status = 'stopped'::public.trip_status) AND (tl.stopped_reported_at >= (CURRENT_DATE - '90 days'::interval)))
  GROUP BY dt.truck_number, dt.id, d.full_name
  WITH NO DATA;


--
-- Name: MATERIALIZED VIEW mv_stopped_analytics_summary; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON MATERIALIZED VIEW public.mv_stopped_analytics_summary IS 'Summary analytics for stopped trips (formerly breakdown analytics)';


--
-- Name: mv_trip_performance_summary; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.mv_trip_performance_summary AS
 SELECT date_trunc('day'::text, tl.created_at) AS trip_date,
    dt.truck_number,
    d.full_name AS driver_name,
    ll.name AS loading_location,
    ul.name AS unloading_location,
    count(*) AS total_trips,
    count(
        CASE
            WHEN (tl.status = 'trip_completed'::public.trip_status) THEN 1
            ELSE NULL::integer
        END) AS completed_trips,
    count(
        CASE
            WHEN tl.is_exception THEN 1
            ELSE NULL::integer
        END) AS exception_trips,
    avg(tl.total_duration_minutes) AS avg_duration,
    avg(tl.loading_duration_minutes) AS avg_loading_duration,
    avg(tl.travel_duration_minutes) AS avg_travel_duration,
    avg(tl.unloading_duration_minutes) AS avg_unloading_duration,
    round((((count(
        CASE
            WHEN tl.is_exception THEN 1
            ELSE NULL::integer
        END))::numeric / (NULLIF(count(*), 0))::numeric) * (100)::numeric), 2) AS exception_rate_percent
   FROM (((((public.trip_logs tl
     JOIN public.assignments a ON ((tl.assignment_id = a.id)))
     JOIN public.dump_trucks dt ON ((a.truck_id = dt.id)))
     LEFT JOIN public.drivers d ON ((a.driver_id = d.id)))
     LEFT JOIN public.locations ll ON ((a.loading_location_id = ll.id)))
     LEFT JOIN public.locations ul ON ((a.unloading_location_id = ul.id)))
  WHERE (tl.created_at >= (CURRENT_DATE - '90 days'::interval))
  GROUP BY (date_trunc('day'::text, tl.created_at)), dt.truck_number, d.full_name, ll.name, ul.name
  WITH NO DATA;


--
-- Name: MATERIALIZED VIEW mv_trip_performance_summary; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON MATERIALIZED VIEW public.mv_trip_performance_summary IS 'Pre-aggregated trip performance data for fast dashboard queries';


--
-- Name: role_permissions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.role_permissions (
    id integer NOT NULL,
    role_name public.user_role NOT NULL,
    page_key character varying(100) NOT NULL,
    has_access boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: TABLE role_permissions; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.role_permissions IS 'Stores page access permissions for each user role';


--
-- Name: COLUMN role_permissions.role_name; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.role_permissions.role_name IS 'User role from user_role enum';


--
-- Name: COLUMN role_permissions.page_key; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.role_permissions.page_key IS 'Unique identifier for application page/route';


--
-- Name: COLUMN role_permissions.has_access; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.role_permissions.has_access IS 'Whether the role has access to this page';


--
-- Name: role_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.role_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: role_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.role_permissions_id_seq OWNED BY public.role_permissions.id;


--
-- Name: scan_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.scan_logs (
    id integer NOT NULL,
    trip_log_id integer,
    scan_type public.scan_type NOT NULL,
    scanned_data text NOT NULL,
    scanned_location_id integer,
    scanned_truck_id integer,
    scanner_user_id integer,
    scan_timestamp timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_valid boolean DEFAULT true NOT NULL,
    validation_error text,
    ip_address inet,
    user_agent text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


--
-- Name: scan_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.scan_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: scan_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.scan_logs_id_seq OWNED BY public.scan_logs.id;


--
-- Name: security_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.security_logs (
    id integer NOT NULL,
    activity_type character varying(100) NOT NULL,
    ip_address inet,
    user_agent text,
    endpoint character varying(255),
    details jsonb,
    risk_level character varying(20) DEFAULT 'LOW'::character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: TABLE security_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.security_logs IS 'Comprehensive security audit log for tracking suspicious activities and abuse patterns';


--
-- Name: COLUMN security_logs.activity_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.security_logs.activity_type IS 'Type of security event (e.g., FAILED_AUTHENTICATION, RATE_LIMIT_EXCEEDED, SUSPICIOUS_ACTIVITY)';


--
-- Name: COLUMN security_logs.details; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.security_logs.details IS 'JSON details about the security event including context and indicators';


--
-- Name: COLUMN security_logs.risk_level; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.security_logs.risk_level IS 'Risk assessment level: LOW, MEDIUM, HIGH, CRITICAL';


--
-- Name: security_dashboard; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.security_dashboard AS
 SELECT date(created_at) AS log_date,
    activity_type,
    count(*) AS incident_count,
    count(DISTINCT ip_address) AS unique_ips,
    risk_level,
    max(created_at) AS latest_incident
   FROM public.security_logs
  WHERE (created_at >= (CURRENT_DATE - '30 days'::interval))
  GROUP BY (date(created_at)), activity_type, risk_level
  ORDER BY (date(created_at)) DESC, (count(*)) DESC;


--
-- Name: VIEW security_dashboard; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.security_dashboard IS 'Daily security incident summary for monitoring dashboard';


--
-- Name: security_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.security_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: security_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.security_logs_id_seq OWNED BY public.security_logs.id;


--
-- Name: shift_handovers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.shift_handovers (
    id integer NOT NULL,
    truck_id integer NOT NULL,
    outgoing_shift_id integer NOT NULL,
    incoming_shift_id integer NOT NULL,
    active_trip_id integer,
    trip_status_at_handover character varying(50),
    location_at_handover integer,
    handover_time timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    handover_notes text,
    fuel_level numeric(5,2),
    vehicle_condition text,
    approved_by integer,
    approved_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


--
-- Name: shift_handovers_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.shift_handovers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: shift_handovers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.shift_handovers_id_seq OWNED BY public.shift_handovers.id;


--
-- Name: system_health_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.system_health_logs (
    id integer NOT NULL,
    module character varying(50) NOT NULL,
    status character varying(20) NOT NULL,
    issues jsonb,
    metrics jsonb,
    checked_at timestamp with time zone DEFAULT now(),
    CONSTRAINT system_health_logs_status_check CHECK (((status)::text = ANY (ARRAY[('operational'::character varying)::text, ('warning'::character varying)::text, ('critical'::character varying)::text])))
);


--
-- Name: TABLE system_health_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.system_health_logs IS 'Logs of system health status checks';


--
-- Name: COLUMN system_health_logs.module; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_health_logs.module IS 'Module that was checked (shift_management, assignment_management, trip_monitoring)';


--
-- Name: COLUMN system_health_logs.status; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_health_logs.status IS 'Health status of the module';


--
-- Name: COLUMN system_health_logs.issues; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_health_logs.issues IS 'Detailed information about detected issues in JSON format';


--
-- Name: COLUMN system_health_logs.metrics; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_health_logs.metrics IS 'Performance metrics and other data in JSON format';


--
-- Name: COLUMN system_health_logs.checked_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_health_logs.checked_at IS 'When the health check was performed';


--
-- Name: system_health_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.system_health_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: system_health_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.system_health_logs_id_seq OWNED BY public.system_health_logs.id;


--
-- Name: system_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.system_logs (
    id integer NOT NULL,
    log_type character varying(50) NOT NULL,
    message text NOT NULL,
    details jsonb,
    user_id integer,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE system_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.system_logs IS 'System-wide logging for automated and manual operations';


--
-- Name: COLUMN system_logs.log_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_logs.log_type IS 'Type of log entry (e.g., SHIFT_AUTO_ACTIVATION, SHIFT_MANUAL_COMPLETION)';


--
-- Name: COLUMN system_logs.details; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_logs.details IS 'Additional details in JSON format';


--
-- Name: system_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.system_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: system_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.system_logs_id_seq OWNED BY public.system_logs.id;


--
-- Name: system_tasks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.system_tasks (
    id integer NOT NULL,
    type character varying(50) NOT NULL,
    priority character varying(20) DEFAULT 'medium'::character varying NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    created_at timestamp with time zone DEFAULT now(),
    scheduled_for timestamp with time zone,
    completed_at timestamp with time zone,
    estimated_duration integer,
    auto_executable boolean DEFAULT false,
    metadata jsonb,
    created_by integer,
    CONSTRAINT system_tasks_priority_check CHECK (((priority)::text = ANY (ARRAY[('low'::character varying)::text, ('medium'::character varying)::text, ('high'::character varying)::text, ('critical'::character varying)::text]))),
    CONSTRAINT system_tasks_status_check CHECK (((status)::text = ANY (ARRAY[('pending'::character varying)::text, ('in_progress'::character varying)::text, ('completed'::character varying)::text, ('failed'::character varying)::text, ('cancelled'::character varying)::text])))
);


--
-- Name: TABLE system_tasks; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.system_tasks IS 'Maintenance tasks for system health management';


--
-- Name: COLUMN system_tasks.type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.type IS 'Type of task (maintenance, cleanup, monitoring, optimization)';


--
-- Name: COLUMN system_tasks.priority; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.priority IS 'Priority level of the task';


--
-- Name: COLUMN system_tasks.status; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.status IS 'Current status of the task';


--
-- Name: COLUMN system_tasks.title; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.title IS 'Short descriptive title of the task';


--
-- Name: COLUMN system_tasks.description; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.description IS 'Detailed description of the task';


--
-- Name: COLUMN system_tasks.scheduled_for; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.scheduled_for IS 'When the task is scheduled to be executed';


--
-- Name: COLUMN system_tasks.completed_at; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.completed_at IS 'When the task was completed';


--
-- Name: COLUMN system_tasks.estimated_duration; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.estimated_duration IS 'Estimated duration in seconds';


--
-- Name: COLUMN system_tasks.auto_executable; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.auto_executable IS 'Whether the task can be executed automatically';


--
-- Name: COLUMN system_tasks.metadata; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.metadata IS 'Additional task-specific data in JSON format';


--
-- Name: COLUMN system_tasks.created_by; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.system_tasks.created_by IS 'User who created the task';


--
-- Name: system_tasks_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.system_tasks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: system_tasks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.system_tasks_id_seq OWNED BY public.system_tasks.id;


--
-- Name: trip_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.trip_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: trip_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.trip_logs_id_seq OWNED BY public.trip_logs.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    id integer NOT NULL,
    username character varying(50) NOT NULL,
    email character varying(100) NOT NULL,
    password_hash character varying(255) NOT NULL,
    full_name character varying(100) NOT NULL,
    role public.user_role DEFAULT 'operator'::public.user_role NOT NULL,
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    last_login timestamp without time zone,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT users_status_check CHECK (((status)::text = ANY (ARRAY[('active'::character varying)::text, ('inactive'::character varying)::text])))
);


--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: v_active_exceptions; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_active_exceptions AS
 SELECT a.id AS approval_id,
    a.trip_log_id,
    a.exception_type,
    a.exception_description,
    COALESCE(a.severity, 'medium'::character varying) AS severity,
    a.status AS approval_status,
    a.requested_at,
    a.reviewed_at,
    (EXTRACT(epoch FROM (COALESCE((a.reviewed_at)::timestamp with time zone, CURRENT_TIMESTAMP) - (a.requested_at)::timestamp with time zone)) / (3600)::numeric) AS resolution_hours,
    tl.trip_number,
    tl.status AS trip_status,
    dt.truck_number,
    d.full_name AS driver_name,
    ll.name AS assigned_loading_location,
    ul.name AS assigned_unloading_location,
    al.name AS actual_loading_location,
    aul.name AS actual_unloading_location,
    u1.full_name AS reported_by_name,
    u2.full_name AS reviewed_by_name
   FROM ((((((((((public.approvals a
     JOIN public.trip_logs tl ON ((a.trip_log_id = tl.id)))
     JOIN public.assignments ass ON ((tl.assignment_id = ass.id)))
     JOIN public.dump_trucks dt ON ((ass.truck_id = dt.id)))
     LEFT JOIN public.drivers d ON ((ass.driver_id = d.id)))
     LEFT JOIN public.locations ll ON ((ass.loading_location_id = ll.id)))
     LEFT JOIN public.locations ul ON ((ass.unloading_location_id = ul.id)))
     LEFT JOIN public.locations al ON ((tl.actual_loading_location_id = al.id)))
     LEFT JOIN public.locations aul ON ((tl.actual_unloading_location_id = aul.id)))
     LEFT JOIN public.users u1 ON ((a.reported_by = u1.id)))
     LEFT JOIN public.users u2 ON ((a.reviewed_by = u2.id)))
  WHERE (a.created_at >= (CURRENT_DATE - '7 days'::interval))
  ORDER BY a.created_at DESC;


--
-- Name: VIEW v_active_exceptions; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.v_active_exceptions IS 'Real-time view of active exceptions and their resolution status';


--
-- Name: v_dynamic_assignment_analytics; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_dynamic_assignment_analytics AS
 SELECT count(*) AS total_adaptive_assignments,
    count(
        CASE
            WHEN (status = 'assigned'::public.assignment_status) THEN 1
            ELSE NULL::integer
        END) AS active_adaptive_assignments,
    count(
        CASE
            WHEN (status = 'pending_approval'::public.assignment_status) THEN 1
            ELSE NULL::integer
        END) AS pending_adaptive_assignments,
    count(
        CASE
            WHEN ((adaptation_strategy)::text = 'pattern_based'::text) THEN 1
            ELSE NULL::integer
        END) AS pattern_based_count,
    count(
        CASE
            WHEN ((adaptation_strategy)::text = 'proximity_based'::text) THEN 1
            ELSE NULL::integer
        END) AS proximity_based_count,
    count(
        CASE
            WHEN ((adaptation_strategy)::text = 'efficiency_based'::text) THEN 1
            ELSE NULL::integer
        END) AS efficiency_based_count,
    count(
        CASE
            WHEN ((adaptation_strategy)::text = 'manual_override'::text) THEN 1
            ELSE NULL::integer
        END) AS manual_override_count,
    count(
        CASE
            WHEN ((adaptation_confidence)::text = 'high'::text) THEN 1
            ELSE NULL::integer
        END) AS high_confidence_count,
    count(
        CASE
            WHEN ((adaptation_confidence)::text = 'medium'::text) THEN 1
            ELSE NULL::integer
        END) AS medium_confidence_count,
    count(
        CASE
            WHEN ((adaptation_confidence)::text = 'low'::text) THEN 1
            ELSE NULL::integer
        END) AS low_confidence_count,
    round(avg(
        CASE
            WHEN (created_at >= (CURRENT_DATE - '7 days'::interval)) THEN 1
            ELSE 0
        END), 2) AS weekly_creation_rate,
    round((((count(
        CASE
            WHEN (status = 'assigned'::public.assignment_status) THEN 1
            ELSE NULL::integer
        END))::numeric / (NULLIF(count(*), 0))::numeric) * (100)::numeric), 2) AS success_rate_percent,
    CURRENT_TIMESTAMP AS last_updated
   FROM public.assignments
  WHERE (is_adaptive = true);


--
-- Name: v_realtime_dashboard; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_realtime_dashboard AS
 SELECT ( SELECT count(*) AS count
           FROM public.trip_logs
          WHERE ((trip_logs.status = ANY (ARRAY['loading_start'::public.trip_status, 'loading_end'::public.trip_status, 'unloading_start'::public.trip_status, 'unloading_end'::public.trip_status])) AND (date(trip_logs.created_at) = CURRENT_DATE))) AS active_trips,
    ( SELECT count(*) AS count
           FROM public.approvals
          WHERE (approvals.status = 'pending'::public.approval_status)) AS pending_exceptions,
    ( SELECT count(*) AS count
           FROM public.trip_logs
          WHERE ((trip_logs.status = 'trip_completed'::public.trip_status) AND (date(trip_logs.created_at) = CURRENT_DATE))) AS completed_trips_today,
    round((((( SELECT count(*) AS count
           FROM public.trip_logs
          WHERE ((trip_logs.is_exception = true) AND (date(trip_logs.created_at) = CURRENT_DATE))))::numeric / (NULLIF(( SELECT count(*) AS count
           FROM public.trip_logs
          WHERE (date(trip_logs.created_at) = CURRENT_DATE)), 0))::numeric) * (100)::numeric), 2) AS exception_rate_today,
    round(( SELECT avg(trip_logs.total_duration_minutes) AS avg
           FROM public.trip_logs
          WHERE ((trip_logs.status = 'trip_completed'::public.trip_status) AND (date(trip_logs.created_at) = CURRENT_DATE))), 2) AS avg_trip_duration_today,
    ( SELECT count(DISTINCT a.truck_id) AS count
           FROM public.assignments a
          WHERE ((a.status = ANY (ARRAY['assigned'::public.assignment_status, 'in_progress'::public.assignment_status])) AND (a.assigned_date = CURRENT_DATE))) AS active_trucks,
    CURRENT_TIMESTAMP AS last_updated;


--
-- Name: VIEW v_realtime_dashboard; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.v_realtime_dashboard IS 'Real-time dashboard metrics for operational monitoring';


--
-- Name: v_trip_performance; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_trip_performance AS
 SELECT date(tl.created_at) AS trip_date,
    dt.truck_number,
    d.full_name AS driver_name,
    count(DISTINCT tl.id) AS total_trips,
    count(DISTINCT
        CASE
            WHEN (tl.status = 'trip_completed'::public.trip_status) THEN tl.id
            ELSE NULL::integer
        END) AS completed_trips,
    count(DISTINCT
        CASE
            WHEN tl.is_exception THEN tl.id
            ELSE NULL::integer
        END) AS exception_trips,
    avg(tl.total_duration_minutes) AS avg_trip_duration,
    avg(tl.loading_duration_minutes) AS avg_loading_time,
    avg(tl.unloading_duration_minutes) AS avg_unloading_time,
    avg(tl.travel_duration_minutes) AS avg_travel_time,
    round((((count(DISTINCT
        CASE
            WHEN tl.is_exception THEN tl.id
            ELSE NULL::integer
        END))::numeric / (NULLIF(count(DISTINCT tl.id), 0))::numeric) * (100)::numeric), 2) AS exception_rate
   FROM (((public.trip_logs tl
     JOIN public.assignments a ON ((tl.assignment_id = a.id)))
     JOIN public.dump_trucks dt ON ((a.truck_id = dt.id)))
     LEFT JOIN public.drivers d ON ((a.driver_id = d.id)))
  WHERE (tl.created_at >= (CURRENT_DATE - '30 days'::interval))
  GROUP BY (date(tl.created_at)), dt.truck_number, d.full_name
  ORDER BY (date(tl.created_at)) DESC, dt.truck_number;


--
-- Name: VIEW v_trip_performance; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.v_trip_performance IS 'Trip performance metrics aggregated by date and truck';


--
-- Name: v_trip_summary; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_trip_summary AS
 SELECT tl.id,
    a.assignment_code,
    a.assigned_date,
    dt.truck_number,
    d.full_name AS driver_name,
    tl.trip_number,
    tl.status,
    tl.previous_status,
    tl.loading_start_time,
    tl.trip_completed_time,
    tl.total_duration_minutes,
    tl.loading_duration_minutes,
    tl.travel_duration_minutes,
    tl.unloading_duration_minutes,
    tl.is_exception,
    tl.exception_reason,
    tl.stopped_reported_at,
    tl.stopped_reason,
    tl.stopped_resolved_at,
    ll.name AS loading_location,
    ul.name AS unloading_location,
    COALESCE(al.name, ll.name) AS actual_loading_location,
    COALESCE(aul.name, ul.name) AS actual_unloading_location
   FROM (((((((public.trip_logs tl
     JOIN public.assignments a ON ((tl.assignment_id = a.id)))
     JOIN public.dump_trucks dt ON ((a.truck_id = dt.id)))
     JOIN public.drivers d ON ((a.driver_id = d.id)))
     LEFT JOIN public.locations ll ON ((a.loading_location_id = ll.id)))
     LEFT JOIN public.locations ul ON ((a.unloading_location_id = ul.id)))
     LEFT JOIN public.locations al ON ((tl.actual_loading_location_id = al.id)))
     LEFT JOIN public.locations aul ON ((tl.actual_unloading_location_id = aul.id)))
  ORDER BY tl.created_at DESC;


--
-- Name: v_workflow_analytics; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.v_workflow_analytics AS
 SELECT workflow_type,
    count(*) AS total_trips,
    count(
        CASE
            WHEN (status = 'trip_completed'::public.trip_status) THEN 1
            ELSE NULL::integer
        END) AS completed_trips,
    avg(total_duration_minutes) AS avg_duration_minutes,
    avg(cycle_number) AS avg_cycle_number
   FROM public.trip_logs
  WHERE (workflow_type IS NOT NULL)
  GROUP BY workflow_type;


--
-- Name: VIEW v_workflow_analytics; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.v_workflow_analytics IS 'Analytics view for multi-location workflow performance metrics';


--
-- Name: approvals id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.approvals ALTER COLUMN id SET DEFAULT nextval('public.approvals_id_seq'::regclass);


--
-- Name: assignments id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments ALTER COLUMN id SET DEFAULT nextval('public.assignments_id_seq'::regclass);


--
-- Name: automated_fix_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.automated_fix_logs ALTER COLUMN id SET DEFAULT nextval('public.automated_fix_logs_id_seq'::regclass);


--
-- Name: driver_shifts id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts ALTER COLUMN id SET DEFAULT nextval('public.driver_shifts_id_seq'::regclass);


--
-- Name: driver_status_audit id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_status_audit ALTER COLUMN id SET DEFAULT nextval('public.driver_status_audit_id_seq'::regclass);


--
-- Name: drivers id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers ALTER COLUMN id SET DEFAULT nextval('public.drivers_id_seq'::regclass);


--
-- Name: dump_trucks id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dump_trucks ALTER COLUMN id SET DEFAULT nextval('public.dump_trucks_id_seq'::regclass);


--
-- Name: health_check_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.health_check_logs ALTER COLUMN id SET DEFAULT nextval('public.health_check_logs_id_seq'::regclass);


--
-- Name: locations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.locations ALTER COLUMN id SET DEFAULT nextval('public.locations_id_seq'::regclass);


--
-- Name: migration_log id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migration_log ALTER COLUMN id SET DEFAULT nextval('public.migration_log_id_seq'::regclass);


--
-- Name: migrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migrations ALTER COLUMN id SET DEFAULT nextval('public.migrations_id_seq'::regclass);


--
-- Name: role_permissions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_permissions ALTER COLUMN id SET DEFAULT nextval('public.role_permissions_id_seq'::regclass);


--
-- Name: scan_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scan_logs ALTER COLUMN id SET DEFAULT nextval('public.scan_logs_id_seq'::regclass);


--
-- Name: security_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.security_logs ALTER COLUMN id SET DEFAULT nextval('public.security_logs_id_seq'::regclass);


--
-- Name: shift_handovers id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers ALTER COLUMN id SET DEFAULT nextval('public.shift_handovers_id_seq'::regclass);


--
-- Name: system_health_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_health_logs ALTER COLUMN id SET DEFAULT nextval('public.system_health_logs_id_seq'::regclass);


--
-- Name: system_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_logs ALTER COLUMN id SET DEFAULT nextval('public.system_logs_id_seq'::regclass);


--
-- Name: system_tasks id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_tasks ALTER COLUMN id SET DEFAULT nextval('public.system_tasks_id_seq'::regclass);


--
-- Name: trip_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs ALTER COLUMN id SET DEFAULT nextval('public.trip_logs_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Data for Name: approvals; Type: TABLE DATA; Schema: public; Owner: -
--



--
-- Data for Name: assignments; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.assignments VALUES (29, 'ASG-1756053759431-D6UA3C', 2, 21, 1, 2, 'assigned', 'normal', '2025-08-24', NULL, NULL, 10, false, NULL, NULL, NULL, '[No active driver found - manual assignment required] [No active driver found - manual assignment required]', '2025-08-25 00:42:39.438914', '2025-08-25 00:43:07.278779', NULL, false, NULL, true);
INSERT INTO public.assignments VALUES (30, 'DYN-1757597781766-E7EU95', 2, 21, 1, 3, 'assigned', 'normal', '2025-09-11', NULL, NULL, 10, false, NULL, NULL, NULL, '{"creation_method":"dynamic_assignment","created_by_user_id":-1,"trigger_location":{"id":3,"name":"Point C - Secondary Dump Site","type":"unloading"},"based_on_assignment":{"id":29,"assignment_code":"ASG-1756053759431-D6UA3C"},"route_discovery":{"mode":"progressive","discovery_type":"loading_confirmed","confirmed_location":{"id":3,"name":"Point C - Secondary Dump Site","type":"unloading","role":"unloading"},"predicted_location":{"loading_id":1,"unloading_id":3,"based_on_assignment":"ASG-1756053759431-D6UA3C"},"description":"Point A - Main Loading Site → Point C - Secondary Dump Site (route deviation auto-assignment)","needs_confirmation":true},"auto_created":true,"requires_review":false}', '2025-09-11 21:36:21.77', '2025-09-11 21:36:21.77', NULL, false, NULL, false);
INSERT INTO public.assignments VALUES (31, 'DYN-1757597844244-IEOXGY', 2, 21, 5, 3, 'assigned', 'normal', '2025-09-11', NULL, NULL, 10, false, NULL, NULL, NULL, '{"creation_method":"dynamic_assignment","created_by_user_id":-1,"trigger_location":{"id":5,"name":"Point C - Secondary Loading Site","type":"loading"},"based_on_assignment":{"id":30,"assignment_code":"DYN-1757597781766-E7EU95"},"route_discovery":{"mode":"progressive","discovery_type":"independent_unloading_discovery","confirmed_location":{"id":5,"name":"Point C - Secondary Loading Site","type":"loading","role":"loading"},"predicted_location":{"loading_id":5,"unloading_id":3,"based_on_assignment":"DYN-1757597781766-E7EU95"},"description":"Point C - Secondary Loading Site → [Point C - Secondary Dump Site - placeholder for discovery]","needs_confirmation":true},"auto_created":true,"requires_review":false}', '2025-09-11 21:37:24.248', '2025-09-11 21:37:24.248', NULL, false, NULL, false);
INSERT INTO public.assignments VALUES (32, 'DYN-1757597984457-AO0U7F', 2, 21, 5, 2, 'assigned', 'normal', '2025-09-11', NULL, NULL, 10, false, NULL, NULL, NULL, '{"creation_method":"dynamic_assignment","created_by_user_id":-1,"trigger_location":{"id":2,"name":"Point B - Primary Dump Site","type":"unloading"},"based_on_assignment":{"id":31,"assignment_code":"DYN-1757597844244-IEOXGY"},"route_discovery":{"mode":"progressive","discovery_type":"loading_confirmed","confirmed_location":{"id":2,"name":"Point B - Primary Dump Site","type":"unloading","role":"unloading"},"predicted_location":{"loading_id":5,"unloading_id":2,"based_on_assignment":"DYN-1757597844244-IEOXGY"},"description":"Point C - Secondary Loading Site → Point B - Primary Dump Site (route deviation auto-assignment)","needs_confirmation":true},"auto_created":true,"requires_review":false}', '2025-09-11 21:39:44.459', '2025-09-11 21:39:44.459', NULL, false, NULL, false);


--
-- Data for Name: automated_fix_logs; Type: TABLE DATA; Schema: public; Owner: -
--



--
-- Data for Name: driver_shifts; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.driver_shifts VALUES (198, 2, 21, 'night', '21:55:46', '21:57:32', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-11 21:55:46.295', '2025-09-11 21:57:32.826', '2025-09-11', '2025-09-11', 'single', 'night', '2025-09-11', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (199, 2, 21, 'night', '22:14:00', '22:17:01', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-11 22:14:00.855', '2025-09-11 22:17:01.04', '2025-09-11', '2025-09-11', 'single', 'night', '2025-09-11', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (188, 1, 20, 'day', '17:41:12', '01:09:44', 'completed', NULL, NULL, NULL, NULL, true, '2025-08-21 17:41:12.604', '2025-08-25 01:09:44.555', '2025-08-21', '2025-08-24', 'single', 'day', '2025-08-21', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (195, 2, 21, 'night', '00:41:24', '21:42:19', 'completed', NULL, NULL, NULL, NULL, true, '2025-08-25 00:41:24.799', '2025-09-11 21:42:19.358', '2025-08-24', '2025-09-11', 'single', 'night', '2025-08-24', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (197, 2, 21, 'night', '21:52:05', '21:54:19', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-11 21:52:05.295', '2025-09-11 21:54:19.858', '2025-09-11', '2025-09-11', 'single', 'night', '2025-09-11', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (200, 2, 21, 'night', '22:59:17', '23:00:17', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-11 22:59:17.532', '2025-09-11 23:00:17.579', '2025-09-11', '2025-09-11', 'single', 'night', '2025-09-11', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (201, 2, 21, 'night', '23:02:12', '23:02:34', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-11 23:02:12.111', '2025-09-11 23:02:34.356', '2025-09-11', '2025-09-11', 'single', 'night', '2025-09-11', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (202, 2, 21, 'night', '23:03:23', '23:04:16', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-11 23:03:23.996', '2025-09-11 23:04:16.995', '2025-09-11', '2025-09-11', 'single', 'night', '2025-09-11', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (203, 2, 21, 'night', '23:45:58', '23:47:10', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-11 23:45:58.683', '2025-09-11 23:47:10.784', '2025-09-11', '2025-09-11', 'single', 'night', '2025-09-11', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (204, 2, 21, 'night', '00:01:43', '00:01:54', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 00:01:43.84', '2025-09-12 00:01:54.638', '2025-09-12', '2025-09-12', 'single', 'night', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (205, 2, 21, 'night', '00:02:07', '00:19:03', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 00:02:07.786', '2025-09-12 00:19:03.758', '2025-09-12', '2025-09-12', 'single', 'night', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (206, 2, 21, 'night', '00:31:02', '00:31:56', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 00:31:02.256', '2025-09-12 00:31:56.615', '2025-09-12', '2025-09-12', 'single', 'night', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (207, 2, 21, 'night', '00:40:34', '00:41:16', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 00:40:34.339', '2025-09-12 00:41:16.107', '2025-09-12', '2025-09-12', 'single', 'night', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (208, 2, 21, 'night', '00:41:47', '00:43:31', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 00:41:47.655', '2025-09-12 00:43:31.091', '2025-09-12', '2025-09-12', 'single', 'night', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (209, 2, 21, 'night', '00:44:50', '00:46:54', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 00:44:50.736', '2025-09-12 00:46:54.095', '2025-09-12', '2025-09-12', 'single', 'night', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (210, 2, 21, 'night', '01:00:36', '01:01:08', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 01:00:36.13', '2025-09-12 01:01:08.737', '2025-09-12', '2025-09-12', 'single', 'night', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (211, 2, 21, 'night', '01:03:39', '01:05:10', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 01:03:39.752', '2025-09-12 01:05:10.928', '2025-09-12', '2025-09-12', 'single', 'night', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (212, 2, 21, 'night', '01:10:01', '01:15:30', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 01:10:01.425', '2025-09-12 01:15:30.81', '2025-09-12', '2025-09-12', 'single', 'night', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (213, 2, 21, 'night', '01:23:03', '01:39:22', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 01:23:03.013', '2025-09-12 01:39:22.181', '2025-09-12', '2025-09-12', 'single', 'night', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (214, 2, 21, 'night', '01:41:25', '01:43:34', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 01:41:25.059', '2025-09-12 01:43:34.848', '2025-09-12', '2025-09-12', 'single', 'night', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (218, 2, 21, 'night', '02:07:55', '14:03:02', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 02:07:55.081', '2025-09-12 14:03:02.105', '2025-09-12', '2025-09-12', 'single', 'night', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (219, 2, 21, 'day', '14:03:14', '14:05:29', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 14:03:14.578', '2025-09-12 14:05:29.781', '2025-09-12', '2025-09-12', 'single', 'day', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (220, 2, 21, 'day', '14:24:54', '14:43:04', 'completed', NULL, NULL, NULL, NULL, true, '2025-09-12 14:24:54.447', '2025-09-12 14:43:04.934', '2025-09-12', '2025-09-12', 'single', 'day', '2025-09-12', NULL, NULL, NULL, '[]');
INSERT INTO public.driver_shifts VALUES (221, 2, 21, 'day', '15:05:43', NULL, 'active', NULL, NULL, NULL, NULL, true, '2025-09-12 15:05:43.95', '2025-09-12 15:05:43.95', '2025-09-12', NULL, 'single', 'day', '2025-09-12', NULL, NULL, NULL, '[]');


--
-- Data for Name: driver_status_audit; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.driver_status_audit VALUES (1, 36, 'DR-INACTIVE', 'inactive', 'test_operation', '2025-08-08 14:55:41.001366', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 14:55:41.001366');
INSERT INTO public.driver_status_audit VALUES (2, 39, 'DR-SUSPENDED', 'suspended', 'test_operation', '2025-08-08 14:55:41.133519', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 14:55:41.133519');
INSERT INTO public.driver_status_audit VALUES (3, 37, 'DR-ONLEAVE', 'on_leave', 'test_operation', '2025-08-08 14:55:41.186269', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 14:55:41.186269');
INSERT INTO public.driver_status_audit VALUES (4, 38, 'DR-TERMINATED', 'terminated', 'test_operation', '2025-08-08 14:55:41.230509', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 14:55:41.230509');
INSERT INTO public.driver_status_audit VALUES (5, 36, 'DR-INACTIVE', 'inactive', 'check_in', '2025-08-08 15:03:05.528416', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:03:05.528416');
INSERT INTO public.driver_status_audit VALUES (6, 39, 'DR-SUSPENDED', 'suspended', 'qr_scan', '2025-08-08 15:03:06.143078', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:03:06.143078');
INSERT INTO public.driver_status_audit VALUES (7, 37, 'DR-ONLEAVE', 'on_leave', 'check_out', '2025-08-08 15:03:06.260741', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:03:06.260741');
INSERT INTO public.driver_status_audit VALUES (8, 38, 'DR-TERMINATED', 'terminated', 'trip_start', '2025-08-08 15:03:06.696308', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:03:06.696308');
INSERT INTO public.driver_status_audit VALUES (9, 36, 'DR-INACTIVE', 'inactive', 'check_out', '2025-08-08 15:03:08.078308', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:03:08.078308');
INSERT INTO public.driver_status_audit VALUES (10, 36, 'DR-INACTIVE', 'inactive', 'check_in', '2025-08-08 15:05:52.953702', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:05:52.953702');
INSERT INTO public.driver_status_audit VALUES (11, 39, 'DR-SUSPENDED', 'suspended', 'qr_scan', '2025-08-08 15:05:52.992919', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:05:52.992919');
INSERT INTO public.driver_status_audit VALUES (12, 37, 'DR-ONLEAVE', 'on_leave', 'check_out', '2025-08-08 15:05:53.007424', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:05:53.007424');
INSERT INTO public.driver_status_audit VALUES (13, 38, 'DR-TERMINATED', 'terminated', 'trip_start', '2025-08-08 15:05:53.028786', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:05:53.028786');
INSERT INTO public.driver_status_audit VALUES (14, 36, 'DR-INACTIVE', 'inactive', 'audit_test_operation', '2025-08-08 15:05:53.048792', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:05:53.048792');
INSERT INTO public.driver_status_audit VALUES (15, 22, 'DR-003', 'inactive', 'status_check', '2025-08-08 15:14:58.299424', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:14:58.299424');
INSERT INTO public.driver_status_audit VALUES (16, 22, 'DR-003', 'inactive', 'status_check', '2025-08-08 15:16:29.009222', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:16:29.009222');
INSERT INTO public.driver_status_audit VALUES (17, 22, 'DR-003', 'inactive', 'status_check', '2025-08-08 15:16:29.20124', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 15:16:29.20124');
INSERT INTO public.driver_status_audit VALUES (18, 22, 'DR-003', 'inactive', 'status_check', '2025-08-08 20:29:18.772079', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 20:29:18.772079');
INSERT INTO public.driver_status_audit VALUES (19, 22, 'DR-003', 'inactive', 'status_check', '2025-08-08 20:29:35.706534', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 20:29:35.706534');
INSERT INTO public.driver_status_audit VALUES (20, 21, 'DR-002', 'suspended', 'status_check', '2025-08-08 21:29:06.949351', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 21:29:06.949351');
INSERT INTO public.driver_status_audit VALUES (21, 21, 'DR-002', 'suspended', 'status_check', '2025-08-08 21:29:19.047306', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 21:29:19.047306');
INSERT INTO public.driver_status_audit VALUES (22, 21, 'DR-002', 'terminated', 'status_check', '2025-08-08 21:29:40.043492', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 21:29:40.043492');
INSERT INTO public.driver_status_audit VALUES (23, 21, 'DR-002', 'terminated', 'status_check', '2025-08-08 21:29:55.902664', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 21:29:55.902664');
INSERT INTO public.driver_status_audit VALUES (24, 21, 'DR-002', 'on_leave', 'status_check', '2025-08-08 21:30:20.086151', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 21:30:20.086151');
INSERT INTO public.driver_status_audit VALUES (25, 21, 'DR-002', 'on_leave', 'status_check', '2025-08-08 21:30:23.000007', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 21:30:23.000007');
INSERT INTO public.driver_status_audit VALUES (26, 21, 'DR-002', 'inactive', 'status_check', '2025-08-08 21:30:34.588247', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 21:30:34.588247');
INSERT INTO public.driver_status_audit VALUES (27, 21, 'DR-002', 'inactive', 'status_check', '2025-08-08 21:30:40.931331', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-08 21:30:40.931331');
INSERT INTO public.driver_status_audit VALUES (28, 20, 'DR-001', 'inactive', 'status_check', '2025-08-18 19:43:41.955273', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-18 19:43:41.955273');
INSERT INTO public.driver_status_audit VALUES (29, 36, 'DR-INACTIVE', 'inactive', 'sync_test', '2025-08-18 20:22:55.265143', 'PWA_SYSTEM', 'PWA_DRIVER_CONNECT', '2025-08-18 20:22:55.265143');


--
-- Data for Name: drivers; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.drivers VALUES (36, 'DR-INACTIVE', 'Inactive Driver', 'CDL-INACTIVE-001', '2026-12-31', '+1-555-0199', '<EMAIL>', NULL, '2023-01-01', 'inactive', 'Test driver for inactive status validation', '2025-08-08 14:50:45.914616', '2025-08-18 20:42:38.073328', NULL);
INSERT INTO public.drivers VALUES (39, 'DR-SUSPENDED', 'Suspended Driver', 'CDL-SUSPENDED-001', '2026-12-31', '+1-555-0198', '<EMAIL>', NULL, '2023-01-01', 'suspended', 'Test driver for suspended status validation', '2025-08-08 14:55:00.695673', '2025-08-18 20:42:38.075313', NULL);
INSERT INTO public.drivers VALUES (20, 'DR-001', 'Aries Evans', 'CDL1234567890', '2026-12-29', '', '', '', '2025-06-29', 'active', '', '2025-07-29 14:28:54.893587', '2025-08-18 20:54:59.692624', '{"id": "DR-001", "type": "driver", "checksum": "230115c1", "driver_id": 20, "employee_id": "DR-001", "generated_date": "2025-07-29T06:30:43.102Z"}');
INSERT INTO public.drivers VALUES (37, 'DR-ONLEAVE', 'On Leave Driver', 'CDL-ONLEAVE-001', '2026-12-31', '+1-555-0197', '<EMAIL>', NULL, '2023-01-01', 'on_leave', 'Test driver for on_leave status validation', '2025-08-08 14:50:46.162739', '2025-08-08 14:55:00.70616', NULL);
INSERT INTO public.drivers VALUES (38, 'DR-TERMINATED', 'Terminated Driver', 'CDL-TERMINATED-001', '2026-12-31', '+1-555-0196', '<EMAIL>', NULL, '2023-01-01', 'terminated', 'Test driver for terminated status validation', '2025-08-08 14:50:46.170316', '2025-08-08 14:55:00.713405', NULL);
INSERT INTO public.drivers VALUES (22, 'DR-003', 'Maria Garcia', 'CDL5678904321', '2026-12-27', '', '', '', '2025-06-29', 'active', '', '2025-07-29 14:30:03.213529', '2025-08-08 21:28:22.345542', '{"id": "DR-003", "type": "driver", "checksum": "b1ee331e", "driver_id": 22, "employee_id": "DR-003", "generated_date": "2025-07-29T06:40:20.026Z"}');
INSERT INTO public.drivers VALUES (21, 'DR-002', 'John Smith', 'CDL0987654321', '2026-12-26', '', '', '', '2025-06-27', 'active', '', '2025-07-29 14:29:27.774565', '2025-08-08 21:30:50.969265', '{"id": "DR-002", "type": "driver", "checksum": "c1f3c8de", "driver_id": 21, "employee_id": "DR-002", "generated_date": "2025-07-29T06:39:33.835Z"}');


--
-- Data for Name: dump_trucks; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.dump_trucks VALUES (3, 'DT-102', 'TRK-003', 'Peterbilt', '567', 2023, 16.50, '{"id": "DT-102", "type": "truck", "driver_id": "DR-003", "timestamp": "2025-01-01T00:00:00Z", "assigned_route": "A-C"}', 'active', '{"end_of_day": true, "last_trip_id": 36, "checkpoint_time": "2025-08-07T13:21:58.182Z", "checkpoint_location": "TRIP STOPPING POINT", "checkpoint_completed": true}', '2025-07-20 15:01:27.476599', '2025-08-07 21:21:58.222696');
INSERT INTO public.dump_trucks VALUES (1, 'DT-100', 'TRK-001', 'Volvo', 'VHD', 2022, 15.50, '{"id": "DT-100", "type": "truck", "driver_id": "DR-001", "timestamp": "2025-01-01T00:00:00Z", "assigned_route": "A-B"}', 'active', '', '2025-07-20 15:01:27.476599', '2025-08-20 00:07:40.290083');
INSERT INTO public.dump_trucks VALUES (2, 'DT-101', 'TRK-002', 'Mack', 'Granite', 2021, 18.00, '{"id": "DT-101", "type": "truck", "driver_id": "DR-002", "timestamp": "2025-01-01T00:00:00Z", "assigned_route": "A-B"}', 'active', '{"end_of_day": true, "last_trip_id": 50, "checkpoint_time": "2025-08-24T17:05:56.620Z", "checkpoint_location": "TRIP STOPPING POINT", "checkpoint_completed": true}', '2025-07-20 15:01:27.476599', '2025-08-25 01:05:56.636739');


--
-- Data for Name: health_check_logs; Type: TABLE DATA; Schema: public; Owner: -
--



--
-- Data for Name: locations; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.locations VALUES (1, 'LOC-001', 'Point A - Main Loading Site', 'loading', '123 Industrial Ave, Loading District', '40.7128,-74.0060', '{"id": "LOC-001", "name": "Point A", "type": "location", "timestamp": "2025-01-01T00:00:00Z", "coordinates": "40.7128,-74.0060"}', 'active', NULL, '2025-07-20 14:58:28.006604', '2025-07-20 14:58:28.006604', DEFAULT);
INSERT INTO public.locations VALUES (2, 'LOC-002', 'Point B - Primary Dump Site', 'unloading', '456 Dump Rd, Disposal Area', '40.7589,-73.9851', '{"id": "LOC-002", "name": "Point B", "type": "location", "timestamp": "2025-01-01T00:00:00Z", "coordinates": "40.7589,-73.9851"}', 'active', NULL, '2025-07-20 14:58:28.006604', '2025-07-20 14:58:28.006604', DEFAULT);
INSERT INTO public.locations VALUES (3, 'LOC-003', 'Point C - Secondary Dump Site', 'unloading', '789 Alternative Way, Backup Site', '40.7282,-73.7949', '{"id": "LOC-003", "name": "Point C", "type": "location", "timestamp": "2025-01-01T00:00:00Z", "coordinates": "40.7282,-73.7949"}', 'active', NULL, '2025-07-20 14:58:28.006604', '2025-07-20 14:58:28.006604', DEFAULT);
INSERT INTO public.locations VALUES (4, 'LOC-9999', 'TRIP STOPPING POINT', 'checkpoint', 'trip stopping', '', '{"id": "LOC-9999", "name": "TRIP STOPPING POINT", "type": "location", "timestamp": "2025-07-20T10:46:44.699Z", "coordinates": null}', 'active', '', '2025-07-20 18:45:01.484952', '2025-07-20 18:46:44.700488', DEFAULT);
INSERT INTO public.locations VALUES (5, 'LOC-004', 'Point C - Secondary Loading Site', 'loading', 'Point C Secondary Loading Site', '', '{"id": "LOC-004", "name": "Point C - Secondary Loading Site", "type": "location", "timestamp": "2025-07-26T07:13:36.994Z", "coordinates": null}', 'active', 'Loading Site', '2025-07-26 15:13:36.997349', '2025-07-26 15:13:36.997349', DEFAULT);


--
-- Data for Name: migration_log; Type: TABLE DATA; Schema: public; Owner: -
--



--
-- Data for Name: migrations; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.migrations VALUES (1, '017_role_based_access_control.sql', '2025-07-26 03:08:57.234712');
INSERT INTO public.migrations VALUES (2, '018_driver_qr_code_system.sql', '2025-07-26 19:10:57.166328');
INSERT INTO public.migrations VALUES (3, '019_security_audit_logging.sql', '2025-07-27 09:47:20.823675');
INSERT INTO public.migrations VALUES (4, '020_fix_driver_shifts_date_format.sql', '2025-07-27 11:42:01.068607');
INSERT INTO public.migrations VALUES (5, '021_fix_shift_creation_issues.sql', '2025-07-27 12:05:17.210672');
INSERT INTO public.migrations VALUES (6, '022_fix_qr_shift_status_and_type.sql', '2025-07-27 12:17:21.729377');
INSERT INTO public.migrations VALUES (7, '023_fix_shift_constraint_conflicts.sql', '2025-07-28 00:25:13.017238');
INSERT INTO public.migrations VALUES (8, '024_update_shift_type_detection_logic.sql', '2025-07-28 22:59:25.593338');
INSERT INTO public.migrations VALUES (9, '025_allow_null_end_fields_for_active_shifts.sql', '2025-07-28 22:59:25.669447');
INSERT INTO public.migrations VALUES (10, '026_add_system_scanner_user.sql', '2025-08-08 11:54:32.041703');
INSERT INTO public.migrations VALUES (11, '027_add_trips_performance_indexes.sql', '2025-08-08 12:25:14.217983');
INSERT INTO public.migrations VALUES (12, '028_driver_status_audit.sql', '2025-08-09 08:24:23.712066');
INSERT INTO public.migrations VALUES (13, '029_fix_overnight_shift_end_date_bug.sql', '2025-08-09 08:24:23.812239');


--
-- Data for Name: role_permissions; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.role_permissions VALUES (30, 'admin', 'drivers', true, '2025-07-26 03:53:56.815241', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (31, 'admin', 'locations', true, '2025-07-26 03:53:56.815241', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (32, 'admin', 'scanner', true, '2025-07-26 03:53:56.815241', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (7, 'admin', 'settings', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (5, 'admin', 'shifts', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (3, 'admin', 'trips', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (34, 'admin', 'truck_trip_summary', true, '2025-07-26 03:53:56.815241', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (29, 'admin', 'trucks', true, '2025-07-26 03:53:56.815241', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (2, 'admin', 'users', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (27, 'dispatcher', 'analytics', false, '2025-07-26 03:49:43.239944', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (60, 'dispatcher', 'assignment_monitoring', false, '2025-07-26 03:57:11.004142', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (25, 'dispatcher', 'assignments', true, '2025-07-26 03:49:43.23341', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (22, 'dispatcher', 'dashboard', true, '2025-07-26 03:49:43.218261', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (63, 'dispatcher', 'drivers', true, '2025-07-26 03:57:11.004142', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (64, 'dispatcher', 'locations', false, '2025-07-26 03:57:11.004142', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (65, 'dispatcher', 'scanner', true, '2025-07-26 03:57:11.004142', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (28, 'dispatcher', 'settings', false, '2025-07-26 03:49:43.242363', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (26, 'dispatcher', 'shifts', false, '2025-07-26 03:49:43.236625', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (24, 'dispatcher', 'trips', true, '2025-07-26 03:49:43.229538', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (69, 'dispatcher', 'truck_trip_summary', true, '2025-07-26 03:57:11.004142', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (70, 'dispatcher', 'trucks', false, '2025-07-26 03:57:11.004142', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (23, 'dispatcher', 'users', false, '2025-07-26 03:49:43.226794', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (20, 'operator', 'analytics', false, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (44, 'operator', 'assignment_monitoring', false, '2025-07-26 03:54:45.937219', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (18, 'operator', 'assignments', false, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (15, 'operator', 'dashboard', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (42, 'operator', 'drivers', false, '2025-07-26 03:54:45.937219', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (43, 'operator', 'locations', false, '2025-07-26 03:54:45.937219', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (78, 'operator', 'scanner', false, '2025-07-26 03:57:11.004142', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (21, 'operator', 'settings', false, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (19, 'operator', 'shifts', false, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (17, 'operator', 'trips', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (45, 'operator', 'truck_trip_summary', false, '2025-07-26 03:54:45.937219', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (41, 'operator', 'trucks', false, '2025-07-26 03:54:45.937219', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (16, 'operator', 'users', false, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (13, 'supervisor', 'analytics', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (39, 'supervisor', 'assignment_monitoring', true, '2025-07-26 03:54:19.738166', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (11, 'supervisor', 'assignments', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (8, 'supervisor', 'dashboard', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (36, 'supervisor', 'drivers', true, '2025-07-26 03:54:19.738166', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (37, 'supervisor', 'locations', true, '2025-07-26 03:54:19.738166', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (38, 'supervisor', 'scanner', true, '2025-07-26 03:54:19.738166', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (14, 'supervisor', 'settings', false, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (12, 'supervisor', 'shifts', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (10, 'supervisor', 'trips', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (6, 'admin', 'analytics', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (33, 'admin', 'assignment_monitoring', true, '2025-07-26 03:53:56.815241', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (4, 'admin', 'assignments', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (1, 'admin', 'dashboard', true, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (40, 'supervisor', 'truck_trip_summary', true, '2025-07-26 03:54:19.738166', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (35, 'supervisor', 'trucks', true, '2025-07-26 03:54:19.738166', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (9, 'supervisor', 'users', false, '2025-07-26 03:08:57.234712', '2025-07-26 04:50:25.352398');
INSERT INTO public.role_permissions VALUES (202, 'admin', 'driver_connect', true, '2025-07-26 19:10:57.166328', '2025-07-26 19:10:57.166328');
INSERT INTO public.role_permissions VALUES (203, 'admin', 'driver_attendance', true, '2025-07-26 19:10:57.166328', '2025-07-26 19:10:57.166328');
INSERT INTO public.role_permissions VALUES (204, 'supervisor', 'driver_connect', true, '2025-07-26 19:10:57.166328', '2025-07-26 19:10:57.166328');
INSERT INTO public.role_permissions VALUES (205, 'supervisor', 'driver_attendance', true, '2025-07-26 19:10:57.166328', '2025-07-26 19:10:57.166328');
INSERT INTO public.role_permissions VALUES (206, 'operator', 'driver_connect', false, '2025-07-26 19:10:57.166328', '2025-07-26 19:10:57.166328');
INSERT INTO public.role_permissions VALUES (207, 'operator', 'driver_attendance', false, '2025-07-26 19:10:57.166328', '2025-07-26 19:10:57.166328');


--
-- Data for Name: scan_logs; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.scan_logs VALUES (98, NULL, 'location_scan', '{"type":"location","id":"LOC-9999","name":"TRIP STOPPING POINT","checkpoint":true,"timestamp":"2025-08-08T23:32:49.903Z"}', 4, 1, -1, '2025-08-09 07:32:49.933278', true, NULL, NULL, NULL, '2025-08-09 07:32:49.933278');
INSERT INTO public.scan_logs VALUES (99, 50, 'location_scan', '{"type":"location","id":"LOC-9999","name":"TRIP STOPPING POINT","checkpoint":true,"timestamp":"2025-08-24T17:05:56.620Z"}', 4, 2, -1, '2025-08-25 01:05:56.642242', true, NULL, NULL, NULL, '2025-08-25 01:05:56.642242');


--
-- Data for Name: security_logs; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.security_logs VALUES (1, 'SECURITY_SYSTEM_INITIALIZED', NULL, NULL, NULL, '{"version": "1.0", "features": ["audit_logging", "tamper_detection", "rate_limiting", "abuse_monitoring"], "initialized_at": "2025-07-27T09:47:20.823675+08:00"}', 'LOW', '2025-07-27 09:47:20.823675+08');
INSERT INTO public.security_logs VALUES (2, 'FAILED_AUTHENTICATION', '127.0.0.1', NULL, '/api/driver/connect', '{"failure_type": "AUTHENTICATION_FAILED", "failure_reason": "QR code tamper detection failed - invalid checksum", "attempted_employee_id": "UNKNOWN"}', 'LOW', '2025-07-27 10:23:53.311+08');
INSERT INTO public.security_logs VALUES (3, 'FAILED_AUTHENTICATION', '127.0.0.1', NULL, '/api/driver/connect', '{"failure_type": "AUTHENTICATION_FAILED", "failure_reason": "QR code tamper detection failed - invalid checksum", "attempted_employee_id": "UNKNOWN"}', 'LOW', '2025-07-27 10:25:47.201+08');
INSERT INTO public.security_logs VALUES (4, 'FAILED_AUTHENTICATION', '127.0.0.1', NULL, '/api/driver/connect', '{"failure_type": "AUTHENTICATION_FAILED", "failure_reason": "QR code tamper detection failed - invalid checksum", "attempted_employee_id": "UNKNOWN"}', 'LOW', '2025-07-27 10:32:41.189+08');
INSERT INTO public.security_logs VALUES (5, 'FAILED_AUTHENTICATION', '127.0.0.1', NULL, '/api/driver/connect', '{"failure_type": "AUTHENTICATION_FAILED", "failure_reason": "QR code tamper detection failed - invalid checksum", "attempted_employee_id": "UNKNOWN"}', 'LOW', '2025-07-27 10:36:59.14+08');
INSERT INTO public.security_logs VALUES (6, 'FAILED_AUTHENTICATION', '127.0.0.1', NULL, '/api/driver/connect', '{"failure_type": "AUTHENTICATION_FAILED", "failure_reason": "Missing required field: type", "attempted_employee_id": "UNKNOWN"}', 'LOW', '2025-07-27 12:00:19.673+08');
INSERT INTO public.security_logs VALUES (7, 'FAILED_AUTHENTICATION', '172.17.224.1', NULL, '/api/driver/connect', '{"failure_type": "AUTHENTICATION_FAILED", "failure_reason": "Driver not found or employee ID mismatch", "attempted_employee_id": "UNKNOWN"}', 'LOW', '2025-08-01 19:41:02.563+08');
INSERT INTO public.security_logs VALUES (8, 'FAILED_AUTHENTICATION', '172.17.224.1', NULL, '/api/driver/connect', '{"failure_type": "AUTHENTICATION_FAILED", "failure_reason": "Driver not found or employee ID mismatch", "attempted_employee_id": "UNKNOWN"}', 'LOW', '2025-08-01 19:44:11.864+08');
INSERT INTO public.security_logs VALUES (9, 'FAILED_AUTHENTICATION', '172.17.224.1', NULL, '/api/driver/connect', '{"failure_type": "AUTHENTICATION_FAILED", "failure_reason": "Missing required field: id", "attempted_employee_id": "UNKNOWN"}', 'LOW', '2025-08-04 01:00:48.6+08');
INSERT INTO public.security_logs VALUES (10, 'FAILED_AUTHENTICATION', '172.17.224.1', NULL, '/api/driver/connect', '{"failure_type": "AUTHENTICATION_FAILED", "failure_reason": "Missing required field: driver_id", "attempted_employee_id": "UNKNOWN"}', 'LOW', '2025-08-04 01:01:38.023+08');
INSERT INTO public.security_logs VALUES (11, 'FAILED_AUTHENTICATION', '172.17.224.1', NULL, '/api/driver/connect', '{"failure_type": "AUTHENTICATION_FAILED", "failure_reason": "Missing required field: driver_id", "attempted_employee_id": "UNKNOWN"}', 'LOW', '2025-08-04 01:01:38.104+08');
INSERT INTO public.security_logs VALUES (12, 'FAILED_AUTHENTICATION', '127.0.0.1', NULL, '/api/driver/connect', '{"failure_type": "AUTHENTICATION_FAILED", "failure_reason": "Missing required field: id", "attempted_employee_id": "UNKNOWN"}', 'LOW', '2025-08-21 17:19:13.642+08');


--
-- Data for Name: shift_handovers; Type: TABLE DATA; Schema: public; Owner: -
--



--
-- Data for Name: system_health_logs; Type: TABLE DATA; Schema: public; Owner: -
--



--
-- Data for Name: system_logs; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.system_logs VALUES (1, 'SHIFT_MANUAL_REFRESH', 'Shift statuses manually refreshed', '{"timestamp": "2025-07-20T12:46:48.495Z", "statistics": {"total_count": "6", "active_count": "3", "cancelled_count": "0", "completed_count": "1", "scheduled_count": "2"}}', 1, '2025-07-20 20:46:48.486606+08');
INSERT INTO public.system_logs VALUES (2, 'SHIFT_MANUAL_REFRESH', 'Shift statuses manually refreshed', '{"timestamp": "2025-07-20T12:46:51.867Z", "statistics": {"total_count": "6", "active_count": "3", "cancelled_count": "0", "completed_count": "1", "scheduled_count": "2"}}', 1, '2025-07-20 20:46:51.865763+08');
INSERT INTO public.system_logs VALUES (3, 'DATA_QUALITY_ALERT', 'Driver information capture success rate (0%) is below threshold (50%)', '{"details": {"threshold": "50%", "total_trips": 0, "current_rate": "0%", "missing_breakdown": {}}, "severity": "HIGH", "alert_type": "DRIVER_INFO_LOW_SUCCESS_RATE"}', NULL, '2025-07-28 23:21:04.905927+08');
INSERT INTO public.system_logs VALUES (4, 'DATA_QUALITY_ALERT', 'Notes field quality success rate (0%) is below threshold (50%)', '{"details": {"threshold": "50%", "total_trips": 0, "current_rate": "0%", "notes_breakdown": {}}, "severity": "MEDIUM", "alert_type": "NOTES_LOW_SUCCESS_RATE"}', NULL, '2025-07-28 23:21:04.920694+08');
INSERT INTO public.system_logs VALUES (5, 'DATA_QUALITY_ALERT', 'Location sequence accuracy (0%) is below threshold (50%)', '{"details": {"threshold": "50%", "total_trips": 0, "current_rate": "0%", "sequence_breakdown": {}}, "severity": "HIGH", "alert_type": "SEQUENCE_LOW_SUCCESS_RATE"}', NULL, '2025-07-28 23:21:04.921626+08');
INSERT INTO public.system_logs VALUES (6, 'DATA_QUALITY_ALERT', 'Overall field completeness rate (0%) is below threshold (50%)', '{"details": {"threshold": "50%", "total_trips": 0, "current_rate": "0%", "field_missing_counts": {"notes": 0, "status": 0, "shift_id": 0, "driver_id": 0, "shift_type": 0, "driver_name": 0, "employee_id": 0, "trip_number": 0, "assignment_id": 0, "location_sequence": 0}}, "severity": "CRITICAL", "alert_type": "OVERALL_LOW_COMPLETENESS_RATE"}', NULL, '2025-07-28 23:21:04.922457+08');
INSERT INTO public.system_logs VALUES (7, 'SHIFT_MANUAL_REFRESH', 'Shift statuses manually refreshed', '{"timestamp": "2025-08-08T12:37:12.645Z", "statistics": {"total_count": "9", "active_count": "1", "cancelled_count": "0", "completed_count": "8", "scheduled_count": "0"}}', 1, '2025-08-08 20:37:12.634169+08');
INSERT INTO public.system_logs VALUES (8, 'SHIFT_MANUAL_REFRESH', 'Shift statuses manually refreshed', '{"timestamp": "2025-08-08T12:37:16.053Z", "statistics": {"total_count": "9", "active_count": "1", "cancelled_count": "0", "completed_count": "8", "scheduled_count": "0"}}', 1, '2025-08-08 20:37:16.049234+08');
INSERT INTO public.system_logs VALUES (9, 'SHIFT_MANUAL_REFRESH', 'Shift statuses manually refreshed', '{"timestamp": "2025-08-08T12:37:21.015Z", "statistics": {"total_count": "9", "active_count": "1", "cancelled_count": "0", "completed_count": "8", "scheduled_count": "0"}}', 1, '2025-08-08 20:37:21.010869+08');
INSERT INTO public.system_logs VALUES (10, 'SHIFT_MANUAL_REFRESH', 'Shift statuses manually refreshed', '{"timestamp": "2025-08-09T11:13:05.063Z", "statistics": {"total_count": "10", "active_count": "1", "cancelled_count": "0", "completed_count": "9", "scheduled_count": "0"}}', 1, '2025-08-09 19:13:05.054068+08');


--
-- Data for Name: system_tasks; Type: TABLE DATA; Schema: public; Owner: -
--



--
-- Data for Name: trip_logs; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.trip_logs VALUES (68, 32, 17, 'trip_completed', '2025-09-12 14:38:48.161', '2025-09-12 14:39:24.65', '2025-09-12 14:41:06.131', '2025-09-12 14:41:27.697', '2025-09-12 14:42:46.123', 5, 3, false, NULL, NULL, NULL, 4, 1, 2, 0, '{"workflow_type": "enhanced_trip_completion", "assignment_type": "existing", "previous_trip_id": 67, "completion_method": "driver_connect_pwa", "completion_source": "manual", "return_travel_end": "2025-09-12T06:42:46.123Z", "workflow_completed": "loading_start → loading_end → unloading_start → unloading_end → manual_completion", "return_travel_start": "2025-09-12T06:41:27.697Z", "completion_timestamp": "2025-09-12T06:42:46.123Z", "return_destination_type": "confirmed_loading_location", "auto_started_after_return": true, "return_travel_duration_minutes": 1}', '2025-09-12 14:38:48.161', '2025-09-12 14:42:46.124498', '[{"name": "Point C - Secondary Loading Site", "type": "loading", "confirmed": true, "location_id": 5}, {"name": "Point B - Primary Dump Site", "type": "unloading", "confirmed": true, "location_id": 2}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 220, 'day', NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (59, 30, 10, 'stopped', '2025-09-12 01:04:21.466', '2025-09-12 01:04:43.276', NULL, NULL, NULL, 1, NULL, false, NULL, NULL, NULL, 32, 0, NULL, NULL, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": null, "message": "loading start at Point A - Main Loading Site by unknown driver using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-11T17:04:21.466Z", "stop_method": "driver_connect_pwa", "stop_reason": "Server restarted test", "stop_source": "manual", "trip_number": 10, "workflow_type": "trip_stopping", "stop_timestamp": "2025-09-11T17:36:00.778Z", "assignment_code": "DYN-1757597781766-E7EU95", "workflow_stopped": "manual_stop", "total_duration_minutes": 32}', '2025-09-12 01:04:21.466', '2025-09-12 02:04:53.63288', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point C - Secondary Dump Site", "type": "unloading", "confirmed": false, "location_id": 3}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 211, 'night', '2025-09-12 01:36:00.778', 'Server restarted test', NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (64, 30, 13, 'stopped', '2025-09-12 13:10:44.69', '2025-09-12 13:10:57.627', NULL, NULL, NULL, 1, NULL, false, NULL, NULL, NULL, NULL, 0, NULL, NULL, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": {"id": 21, "name": "John Smith", "shift_id": 218, "shift_type": "night", "employee_id": "DR-002"}, "message": "loading start at Point A - Main Loading Site by John Smith using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-12T05:10:44.690Z", "trip_number": 13, "workflow_type": "standard", "assignment_code": "DYN-1757597781766-E7EU95"}', '2025-09-12 13:10:44.69', '2025-09-12 13:11:24.588187', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point C - Secondary Dump Site", "type": "unloading", "confirmed": false, "location_id": 3}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 218, 'night', '2025-09-12 13:11:24.588187', 'stopp', NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (51, 29, 2, 'trip_completed', '2025-09-11 21:33:20.714', '2025-09-11 21:33:51.656', '2025-09-11 21:34:16.837', '2025-09-11 21:34:30.85', '2025-09-11 21:35:34.794', 1, 2, false, NULL, NULL, NULL, 2, 1, 0, 0, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": {"id": 21, "name": "John Smith", "shift_id": 195, "shift_type": "night", "employee_id": "DR-002"}, "message": "loading start at Point A - Main Loading Site by John Smith using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-11T13:33:20.714Z", "trip_number": 2, "workflow_type": "enhanced_trip_completion", "assignment_code": "ASG-1756053759431-D6UA3C", "completion_method": "admin_dashboard", "completion_source": "admin_manual", "return_travel_end": "2025-09-11T13:35:34.794Z", "workflow_completed": "loading_start → loading_end → unloading_start → unloading_end → admin_completion", "return_travel_start": "2025-09-11T13:34:30.850Z", "completed_by_user_id": 1, "completion_timestamp": "2025-09-11T13:35:34.794Z", "completed_by_user_name": "System Administrator", "return_travel_duration_minutes": 1}', '2025-09-11 21:33:20.714', '2025-09-11 21:35:34.794451', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point B - Primary Dump Site", "type": "unloading", "confirmed": true, "location_id": 2}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 195, 'night', NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (57, 30, 8, 'stopped', '2025-09-11 23:46:47.403', NULL, NULL, NULL, NULL, 1, NULL, false, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": {"id": 21, "name": "John Smith", "shift_id": 203, "shift_type": "night", "employee_id": "DR-002"}, "message": "loading start at Point A - Main Loading Site by John Smith using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-11T15:46:47.403Z", "trip_number": 8, "workflow_type": "standard", "assignment_code": "DYN-1757597781766-E7EU95"}', '2025-09-11 23:46:47.403', '2025-09-12 00:44:34.766783', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point C - Secondary Dump Site", "type": "unloading", "confirmed": false, "location_id": 3}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 203, 'night', '2025-09-12 00:44:34.766783', 'stopped', NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (54, 29, 5, 'trip_completed', '2025-09-11 21:56:04.229', '2025-09-11 21:56:07.559', '2025-09-11 21:56:33.852', '2025-09-11 21:56:47.736', '2025-09-11 22:08:14.907', 1, 2, false, NULL, NULL, NULL, 12, 0, 0, 0, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": {"id": 21, "name": "John Smith", "shift_id": 198, "shift_type": "night", "employee_id": "DR-002"}, "message": "loading start at Point A - Main Loading Site by John Smith using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-11T13:56:04.229Z", "trip_number": 5, "workflow_type": "enhanced_trip_completion", "assignment_code": "ASG-1756053759431-D6UA3C", "completion_method": "admin_dashboard", "completion_source": "admin_manual", "return_travel_end": "2025-09-11T14:08:14.907Z", "workflow_completed": "loading_start → loading_end → unloading_start → unloading_end → admin_completion", "return_travel_start": "2025-09-11T13:56:47.736Z", "completed_by_user_id": 1, "completion_timestamp": "2025-09-11T14:08:14.907Z", "completed_by_user_name": "System Administrator", "return_travel_duration_minutes": 11}', '2025-09-11 21:56:04.229', '2025-09-11 22:08:14.907194', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point B - Primary Dump Site", "type": "unloading", "confirmed": true, "location_id": 2}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 198, 'night', NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (58, 30, 9, 'stopped', '2025-09-12 00:45:38.66', NULL, NULL, NULL, NULL, 1, NULL, false, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": null, "message": "loading start at Point A - Main Loading Site by unknown driver using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-11T16:45:38.660Z", "trip_number": 9, "workflow_type": "standard", "assignment_code": "DYN-1757597781766-E7EU95"}', '2025-09-12 00:45:38.66', '2025-09-12 02:04:53.58799', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point C - Secondary Dump Site", "type": "unloading", "confirmed": false, "location_id": 3}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 203, 'night', '2025-09-12 01:03:22.876705', 'sp', NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (50, 29, 1, 'trip_completed', '2025-08-25 01:03:43.097', '2025-08-25 01:04:11.584', '2025-08-25 01:04:34.556', '2025-08-25 01:04:52.52', '2025-08-25 01:05:56.62', 1, 2, false, NULL, NULL, NULL, 2, 0, 0, 0, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": {"id": 21, "name": "John Smith", "shift_id": 195, "shift_type": "night", "employee_id": "DR-002"}, "message": "loading start at Point A - Main Loading Site by John Smith using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-08-24T17:03:43.097Z", "end_of_day": true, "trip_number": 1, "workflow_type": "standard", "assignment_code": "ASG-1756053759431-D6UA3C", "completion_method": "checkpoint_scan", "return_travel_end": "2025-08-24T17:05:56.620Z", "workflow_completed": "loading_start -> loading_end -> unloading_start -> unloading_end -> checkpoint_completed", "return_travel_start": "2025-08-24T17:04:52.520Z", "checkpoint_completion": true, "checkpoint_location_id": 4, "checkpoint_location_name": "TRIP STOPPING POINT", "return_travel_duration_minutes": 1}', '2025-08-25 01:03:43.097', '2025-08-25 01:05:56.621739', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point B - Primary Dump Site", "type": "unloading", "confirmed": true, "location_id": 2}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 195, 'night', NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (60, 30, 11, 'stopped', '2025-09-12 01:40:14.884', NULL, NULL, NULL, NULL, 1, NULL, false, NULL, NULL, NULL, 3, NULL, NULL, NULL, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": null, "message": "loading start at Point A - Main Loading Site by unknown driver using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-11T17:40:14.884Z", "stop_method": "driver_connect_pwa", "stop_reason": "tire blown", "stop_source": "manual", "trip_number": 11, "workflow_type": "trip_stopping", "stop_timestamp": "2025-09-11T17:43:12.271Z", "assignment_code": "DYN-1757597781766-E7EU95", "workflow_stopped": "manual_stop", "total_duration_minutes": 3}', '2025-09-12 01:40:14.884', '2025-09-12 02:04:53.648593', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point C - Secondary Dump Site", "type": "unloading", "confirmed": false, "location_id": 3}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 213, 'night', '2025-09-12 01:43:12.271', 'tire blown', NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (52, 29, 3, 'trip_completed', '2025-09-11 21:35:54.195', '2025-09-11 21:36:06.036', '2025-09-11 21:36:21.75', '2025-09-11 21:36:56.299', '2025-09-11 21:37:24.206', 1, 3, false, NULL, NULL, NULL, 2, 0, 0, 1, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": {"id": 21, "name": "John Smith", "shift_id": 195, "shift_type": "night", "employee_id": "DR-002"}, "message": "loading start at Point A - Main Loading Site by John Smith using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-11T13:35:54.195Z", "trip_number": 3, "workflow_type": "enhanced_with_return_travel", "assignment_code": "ASG-1756053759431-D6UA3C", "return_travel_end": "2025-09-11T13:37:24.206Z", "return_travel_start": "2025-09-11T13:36:56.299Z", "return_travel_duration_minutes": 0}', '2025-09-11 21:35:54.195', '2025-09-11 21:37:24.217707', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point B - Primary Dump Site", "type": "unloading", "confirmed": true, "location_id": 2}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 195, 'night', NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (55, 29, 6, 'trip_completed', '2025-09-11 22:14:40.27', '2025-09-11 22:14:50.635', '2025-09-11 22:15:38.19', '2025-09-11 22:15:58.729', '2025-09-11 22:57:19.899', 1, 2, false, NULL, NULL, NULL, 43, 0, 1, 0, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": {"id": 21, "name": "John Smith", "shift_id": 199, "shift_type": "night", "employee_id": "DR-002"}, "message": "loading start at Point A - Main Loading Site by John Smith using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-11T14:14:40.270Z", "trip_number": 6, "workflow_type": "enhanced_trip_completion", "assignment_code": "ASG-1756053759431-D6UA3C", "completion_method": "admin_dashboard", "completion_source": "admin_manual", "return_travel_end": "2025-09-11T14:57:19.899Z", "workflow_completed": "loading_start → loading_end → unloading_start → unloading_end → admin_completion", "return_travel_start": "2025-09-11T14:15:58.729Z", "completed_by_user_id": 1, "completion_timestamp": "2025-09-11T14:57:19.899Z", "completed_by_user_name": "System Administrator", "return_travel_duration_minutes": 41}', '2025-09-11 22:14:40.27', '2025-09-11 22:57:19.897635', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point B - Primary Dump Site", "type": "unloading", "confirmed": true, "location_id": 2}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 199, 'night', NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (61, 30, 12, 'stopped', '2025-09-12 02:08:37.987', NULL, NULL, NULL, NULL, 1, NULL, false, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": null, "message": "loading start at Point A - Main Loading Site by unknown driver using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-11T18:08:37.987Z", "trip_number": 12, "workflow_type": "standard", "assignment_code": "DYN-1757597781766-E7EU95"}', '2025-09-12 02:08:37.987', '2025-09-12 03:04:50.805573', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point C - Secondary Dump Site", "type": "unloading", "confirmed": false, "location_id": 3}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 218, 'night', '2025-09-12 02:08:55.719176', 'trip stopped', NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (65, 30, 14, 'stopped', '2025-09-12 13:13:17.254', '2025-09-12 13:13:21.467', NULL, NULL, NULL, 1, NULL, false, NULL, NULL, NULL, 44, 0, NULL, NULL, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": {"id": 21, "name": "John Smith", "shift_id": 218, "shift_type": "night", "employee_id": "DR-002"}, "message": "loading start at Point A - Main Loading Site by John Smith using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-12T05:13:17.254Z", "stop_method": "driver_pwa", "stop_reason": "Test stop via DriverConnect PWA", "stop_source": "driver_manual", "trip_number": 14, "workflow_type": "trip_stopping", "stop_timestamp": "2025-09-12T05:56:54.540Z", "assignment_code": "DYN-1757597781766-E7EU95", "workflow_stopped": "driver_stop", "total_duration_minutes": 44}', '2025-09-12 13:13:17.254', '2025-09-12 13:56:54.543189', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point C - Secondary Dump Site", "type": "unloading", "confirmed": false, "location_id": 3}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 218, 'night', '2025-09-12 13:56:54.533', 'Test stop via DriverConnect PWA', NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (66, 30, 15, 'stopped', '2025-09-12 14:03:54.686', '2025-09-12 14:04:00.968', '2025-09-12 14:04:16.634', NULL, NULL, 1, 2, false, NULL, NULL, NULL, 1, 0, 0, NULL, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": {"id": 21, "name": "John Smith", "shift_id": 219, "shift_type": "day", "employee_id": "DR-002"}, "message": "loading start at Point A - Main Loading Site by John Smith using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-12T06:03:54.686Z", "stop_method": "driver_connect_pwa", "stop_reason": "testing stop trip", "stop_source": "manual", "trip_number": 15, "workflow_type": "trip_stopping", "stop_timestamp": "2025-09-12T06:05:13.507Z", "assignment_code": "DYN-1757597781766-E7EU95", "workflow_stopped": "manual_stop", "total_duration_minutes": 1}', '2025-09-12 14:03:54.686', '2025-09-12 14:05:13.519829', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point C - Secondary Dump Site", "type": "unloading", "confirmed": true, "location_id": 3}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 219, 'day', '2025-09-12 14:05:13.507', 'testing stop trip', NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (53, 31, 4, 'trip_completed', '2025-09-11 21:37:24.206', '2025-09-11 21:39:12.753', '2025-09-11 21:39:44.442', '2025-09-11 21:40:35.153', '2025-09-11 21:55:21.334', 5, 2, false, NULL, NULL, NULL, 18, 2, 1, 1, '{"workflow_type": "enhanced_trip_completion", "assignment_type": "auto_created", "previous_trip_id": 52, "completion_method": "admin_dashboard", "completion_source": "admin_manual", "return_travel_end": "2025-09-11T13:55:21.334Z", "workflow_completed": "loading_start → loading_end → unloading_start → unloading_end → admin_completion", "return_travel_start": "2025-09-11T13:40:35.153Z", "completed_by_user_id": 1, "completion_timestamp": "2025-09-11T13:55:21.334Z", "completed_by_user_name": "System Administrator", "return_destination_type": "unconfirmed_loading_location", "auto_started_after_return": true, "return_travel_duration_minutes": 15}', '2025-09-11 21:37:24.206', '2025-09-11 21:55:21.334226', '[{"name": "Point C - Secondary Loading Site", "type": "loading", "confirmed": true, "location_id": 5}, {"name": "Point C - Secondary Dump Site", "type": "unloading", "confirmed": true, "location_id": 3}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 195, 'night', NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (56, 30, 7, 'stopped', '2025-09-11 23:03:46.033', NULL, NULL, NULL, NULL, 1, NULL, false, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": {"id": 21, "name": "John Smith", "shift_id": 202, "shift_type": "night", "employee_id": "DR-002"}, "message": "loading start at Point A - Main Loading Site by John Smith using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-11T15:03:46.033Z", "trip_number": 7, "workflow_type": "standard", "assignment_code": "DYN-1757597781766-E7EU95"}', '2025-09-11 23:03:46.033', '2025-09-11 23:31:46.754118', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point C - Secondary Dump Site", "type": "unloading", "confirmed": false, "location_id": 3}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 202, 'night', '2025-09-11 23:31:46.754118', 'stop', NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (67, 30, 16, 'trip_completed', '2025-09-12 14:26:12.189', '2025-09-12 14:27:16.058', '2025-09-12 14:27:47.055', '2025-09-12 14:28:25.912', '2025-09-12 14:38:48.161', 1, 2, false, NULL, NULL, NULL, 13, 1, 1, 1, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": {"id": 21, "name": "John Smith", "shift_id": 220, "shift_type": "day", "employee_id": "DR-002"}, "message": "loading start at Point A - Main Loading Site by John Smith using truck DT-101", "location": {"id": 1, "name": "Point A - Main Loading Site", "type": "loading", "location_code": "LOC-001"}, "timestamp": "2025-09-12T06:26:12.189Z", "trip_number": 16, "workflow_type": "enhanced_with_return_travel", "assignment_code": "DYN-1757597781766-E7EU95", "return_travel_end": "2025-09-12T06:38:48.161Z", "return_travel_start": "2025-09-12T06:28:25.912Z", "return_travel_duration_minutes": 10}', '2025-09-12 14:26:12.189', '2025-09-12 14:38:48.186255', '[{"name": "Point A - Main Loading Site", "type": "loading", "confirmed": true, "location_id": 1}, {"name": "Point C - Secondary Dump Site", "type": "unloading", "confirmed": true, "location_id": 3}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 220, 'day', NULL, NULL, NULL, NULL, NULL);
INSERT INTO public.trip_logs VALUES (69, 32, 18, 'trip_completed', '2025-09-12 15:06:32.324', '2025-09-12 15:06:40.187', '2025-09-12 15:07:00.211', '2025-09-12 15:07:04.573', '2025-09-12 15:08:04.306', 5, 2, false, NULL, NULL, NULL, 2, 0, 0, 0, '{"truck": {"id": 2, "truck_number": "DT-101", "license_plate": "TRK-002"}, "action": "loading_start", "driver": {"id": 21, "name": "John Smith", "shift_id": 221, "shift_type": "day", "employee_id": "DR-002"}, "message": "loading start at Point C - Secondary Loading Site by John Smith using truck DT-101", "location": {"id": 5, "name": "Point C - Secondary Loading Site", "type": "loading", "location_code": "LOC-004"}, "timestamp": "2025-09-12T07:06:32.324Z", "trip_number": 18, "workflow_type": "enhanced_trip_completion", "assignment_code": "DYN-1757597984457-AO0U7F", "completion_method": "admin_dashboard", "completion_source": "admin_manual", "return_travel_end": "2025-09-12T07:08:04.306Z", "workflow_completed": "loading_start → loading_end → unloading_start → unloading_end → admin_completion", "return_travel_start": "2025-09-12T07:07:04.573Z", "completed_by_user_id": 1, "completion_timestamp": "2025-09-12T07:08:04.306Z", "completed_by_user_name": "System Administrator", "return_travel_duration_minutes": 1}', '2025-09-12 15:06:32.324', '2025-09-12 15:08:04.306544', '[{"name": "Point C - Secondary Loading Site", "type": "loading", "confirmed": true, "location_id": 5}, {"name": "Point B - Primary Dump Site", "type": "unloading", "confirmed": true, "location_id": 2}]', false, 'standard', NULL, 1, 21, 'John Smith', 'DR-002', 221, 'day', NULL, NULL, NULL, NULL, NULL);


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: -
--

INSERT INTO public.users VALUES (1, 'admin', '<EMAIL>', '$2a$12$PdtEjSPmlpvy2kfksnkIF.PU1uN2CdjFXmp/k8eMY9ArHpvXu0yLq', 'admin one', 'admin', 'active', NULL, '2025-07-20 14:49:34.842156', '2025-07-20 14:49:34.842156');
INSERT INTO public.users VALUES (3, 'admin3', '<EMAIL>', '$2a$12$pwpkdZqjVddjOcmZivUIYeUmjpeIV6mG.ucY4ZdhfCG6iiMXIilyW', 'admin three', 'admin', 'active', NULL, '2025-07-20 14:50:42.748615', '2025-07-20 14:50:42.748615');
INSERT INTO public.users VALUES (4, 'admin4', '<EMAIL>', '$2a$12$HyX6Gvkr3IcMVICkBP7uwerM5nSfK3qrVI1LuikfxrwTF/nmuynem', 'admin four', 'admin', 'active', NULL, '2025-07-20 14:51:12.08422', '2025-07-20 14:51:12.08422');
INSERT INTO public.users VALUES (5, 'testdispatcher', '<EMAIL>', '$2a$12$RMYCKKbTDu/8lGjZeLdscOe2d0b9Dn6fRrv.KXpZEUwEUp6xV0uvW', 'Test Dispatcher', 'dispatcher', 'active', '2025-07-26 04:58:12.958389', '2025-07-26 04:48:41.450039', '2025-07-26 04:58:12.958389');
INSERT INTO public.users VALUES (2, 'admin2', '<EMAIL>', '$2a$12$.sbyGpcu3i5gJez7OjAoMu4esw3DSJZIfZluwDoHTk.NIN3loDnEW', 'admin two', 'admin', 'active', '2025-07-26 14:09:45.453066', '2025-07-20 14:50:12.293723', '2025-07-26 14:09:45.453066');
INSERT INTO public.users VALUES (6, 'dispatcher', '<EMAIL>', '$2a$12$b6S5Yx2VReLoymrXdbDi2O32nARFwuYa0nnNw.Ce72nBqO/fUfvL.', 'dispatcher one', 'dispatcher', 'active', '2025-07-26 15:11:46.032221', '2025-07-26 04:49:15.239566', '2025-07-26 15:11:46.032221');
INSERT INTO public.users VALUES (-1, 'trip-scanner', '<EMAIL>', '$2b$10$dummy.hash.for.system.user.no.login.allowed', 'System Trip Scanner', 'supervisor', 'active', NULL, '2025-08-08 11:54:32.041703', '2025-08-08 11:54:32.041703');


--
-- Name: approvals_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.approvals_id_seq', 1, false);


--
-- Name: assignments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.assignments_id_seq', 32, true);


--
-- Name: automated_fix_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.automated_fix_logs_id_seq', 1, false);


--
-- Name: driver_shifts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.driver_shifts_id_seq', 221, true);


--
-- Name: driver_status_audit_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.driver_status_audit_id_seq', 29, true);


--
-- Name: drivers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.drivers_id_seq', 45, true);


--
-- Name: dump_trucks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.dump_trucks_id_seq', 30, true);


--
-- Name: health_check_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.health_check_logs_id_seq', 1, false);


--
-- Name: locations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.locations_id_seq', 5, true);


--
-- Name: migration_log_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.migration_log_id_seq', 1, false);


--
-- Name: migrations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.migrations_id_seq', 13, true);


--
-- Name: role_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.role_permissions_id_seq', 207, true);


--
-- Name: scan_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.scan_logs_id_seq', 99, true);


--
-- Name: security_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.security_logs_id_seq', 12, true);


--
-- Name: shift_handovers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.shift_handovers_id_seq', 1, false);


--
-- Name: system_health_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.system_health_logs_id_seq', 1, false);


--
-- Name: system_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.system_logs_id_seq', 10, true);


--
-- Name: system_tasks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.system_tasks_id_seq', 1, false);


--
-- Name: trip_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.trip_logs_id_seq', 69, true);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.users_id_seq', 6, true);


--
-- Name: approvals approvals_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.approvals
    ADD CONSTRAINT approvals_pkey PRIMARY KEY (id);


--
-- Name: assignments assignments_assignment_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_assignment_code_key UNIQUE (assignment_code);


--
-- Name: assignments assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_pkey PRIMARY KEY (id);


--
-- Name: automated_fix_logs automated_fix_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.automated_fix_logs
    ADD CONSTRAINT automated_fix_logs_pkey PRIMARY KEY (id);


--
-- Name: driver_shifts driver_shifts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts
    ADD CONSTRAINT driver_shifts_pkey PRIMARY KEY (id);


--
-- Name: driver_status_audit driver_status_audit_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_status_audit
    ADD CONSTRAINT driver_status_audit_pkey PRIMARY KEY (id);


--
-- Name: drivers drivers_employee_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_employee_id_key UNIQUE (employee_id);


--
-- Name: drivers drivers_license_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_license_number_key UNIQUE (license_number);


--
-- Name: drivers drivers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_pkey PRIMARY KEY (id);


--
-- Name: dump_trucks dump_trucks_license_plate_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dump_trucks
    ADD CONSTRAINT dump_trucks_license_plate_key UNIQUE (license_plate);


--
-- Name: dump_trucks dump_trucks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dump_trucks
    ADD CONSTRAINT dump_trucks_pkey PRIMARY KEY (id);


--
-- Name: dump_trucks dump_trucks_truck_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.dump_trucks
    ADD CONSTRAINT dump_trucks_truck_number_key UNIQUE (truck_number);


--
-- Name: health_check_logs health_check_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.health_check_logs
    ADD CONSTRAINT health_check_logs_pkey PRIMARY KEY (id);


--
-- Name: locations locations_location_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.locations
    ADD CONSTRAINT locations_location_code_key UNIQUE (location_code);


--
-- Name: locations locations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.locations
    ADD CONSTRAINT locations_pkey PRIMARY KEY (id);


--
-- Name: migration_log migration_log_migration_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migration_log
    ADD CONSTRAINT migration_log_migration_name_key UNIQUE (migration_name);


--
-- Name: migration_log migration_log_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migration_log
    ADD CONSTRAINT migration_log_pkey PRIMARY KEY (id);


--
-- Name: migrations migrations_filename_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migrations
    ADD CONSTRAINT migrations_filename_key UNIQUE (filename);


--
-- Name: migrations migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (id);


--
-- Name: role_permissions role_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_pkey PRIMARY KEY (id);


--
-- Name: role_permissions role_permissions_role_name_page_key_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_role_name_page_key_key UNIQUE (role_name, page_key);


--
-- Name: scan_logs scan_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scan_logs
    ADD CONSTRAINT scan_logs_pkey PRIMARY KEY (id);


--
-- Name: security_logs security_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.security_logs
    ADD CONSTRAINT security_logs_pkey PRIMARY KEY (id);


--
-- Name: shift_handovers shift_handovers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_pkey PRIMARY KEY (id);


--
-- Name: system_health_logs system_health_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_health_logs
    ADD CONSTRAINT system_health_logs_pkey PRIMARY KEY (id);


--
-- Name: system_logs system_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_logs
    ADD CONSTRAINT system_logs_pkey PRIMARY KEY (id);


--
-- Name: system_tasks system_tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_tasks
    ADD CONSTRAINT system_tasks_pkey PRIMARY KEY (id);


--
-- Name: trip_logs trip_logs_assignment_id_trip_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_assignment_id_trip_number_key UNIQUE (assignment_id, trip_number);


--
-- Name: trip_logs trip_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_pkey PRIMARY KEY (id);


--
-- Name: driver_shifts unique_active_driver_shift; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts
    ADD CONSTRAINT unique_active_driver_shift EXCLUDE USING btree (driver_id WITH =) WHERE ((status = 'active'::public.shift_status));


--
-- Name: CONSTRAINT unique_active_driver_shift ON driver_shifts; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON CONSTRAINT unique_active_driver_shift ON public.driver_shifts IS 'Ensures only one active shift per driver at any time';


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: idx_approvals_adaptation_confidence; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_adaptation_confidence ON public.approvals USING btree (adaptation_confidence) WHERE (adaptation_confidence IS NOT NULL);


--
-- Name: idx_approvals_adaptation_strategy; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_adaptation_strategy ON public.approvals USING btree (adaptation_strategy) WHERE (adaptation_strategy IS NOT NULL);


--
-- Name: idx_approvals_adaptive; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_adaptive ON public.approvals USING btree (is_adaptive_exception) WHERE (is_adaptive_exception = true);


--
-- Name: idx_approvals_adaptive_metadata_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_adaptive_metadata_gin ON public.approvals USING gin (adaptation_metadata) WHERE (adaptation_metadata IS NOT NULL);


--
-- Name: idx_approvals_auto_approved; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_auto_approved ON public.approvals USING btree (auto_approved) WHERE (auto_approved = true);


--
-- Name: idx_approvals_reported_by; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_reported_by ON public.approvals USING btree (reported_by);


--
-- Name: idx_approvals_requested_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_requested_at ON public.approvals USING btree (requested_at);


--
-- Name: idx_approvals_severity; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_severity ON public.approvals USING btree (severity);


--
-- Name: idx_approvals_severity_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_severity_created ON public.approvals USING btree (severity, created_at) WHERE (status = 'pending'::public.approval_status);


--
-- Name: idx_approvals_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_status ON public.approvals USING btree (status);


--
-- Name: idx_approvals_suggested_assignment; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_suggested_assignment ON public.approvals USING btree (suggested_assignment_id) WHERE (suggested_assignment_id IS NOT NULL);


--
-- Name: idx_approvals_trip; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_trip ON public.approvals USING btree (trip_log_id);


--
-- Name: idx_approvals_trip_status_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_approvals_trip_status_created ON public.approvals USING btree (trip_log_id, status, created_at);


--
-- Name: idx_assignments_active_operations; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_active_operations ON public.assignments USING btree (truck_id, status, created_at) WHERE (status = ANY (ARRAY['assigned'::public.assignment_status, 'in_progress'::public.assignment_status]));


--
-- Name: idx_assignments_adaptation_confidence; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_adaptation_confidence ON public.assignments USING btree (adaptation_confidence) WHERE (adaptation_confidence IS NOT NULL);


--
-- Name: idx_assignments_adaptation_metadata_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_adaptation_metadata_gin ON public.assignments USING gin (adaptation_metadata) WHERE (adaptation_metadata IS NOT NULL);


--
-- Name: idx_assignments_adaptation_strategy; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_adaptation_strategy ON public.assignments USING btree (adaptation_strategy) WHERE (adaptation_strategy IS NOT NULL);


--
-- Name: idx_assignments_adaptive; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_adaptive ON public.assignments USING btree (is_adaptive) WHERE (is_adaptive = true);


--
-- Name: idx_assignments_adaptive_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_adaptive_status ON public.assignments USING btree (is_adaptive, status, created_at) WHERE (is_adaptive = true);


--
-- Name: idx_assignments_assigned_date_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_assigned_date_status ON public.assignments USING btree (assigned_date DESC, status);


--
-- Name: idx_assignments_assignment_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_assignment_code ON public.assignments USING btree (assignment_code);


--
-- Name: idx_assignments_auto_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_auto_created ON public.assignments USING btree (auto_created) WHERE (auto_created = true);


--
-- Name: idx_assignments_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_date ON public.assignments USING btree (assigned_date);


--
-- Name: idx_assignments_driver; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_driver ON public.assignments USING btree (driver_id);


--
-- Name: idx_assignments_exact_duplicate; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_assignments_exact_duplicate ON public.assignments USING btree (truck_id, loading_location_id, unloading_location_id) WHERE (status = ANY (ARRAY['assigned'::public.assignment_status, 'in_progress'::public.assignment_status]));


--
-- Name: idx_assignments_locations_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_locations_date ON public.assignments USING btree (loading_location_id, assigned_date, status);


--
-- Name: idx_assignments_priority; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_priority ON public.assignments USING btree (priority);


--
-- Name: idx_assignments_shift; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_shift ON public.assignments USING btree (shift_id) WHERE (shift_id IS NOT NULL);


--
-- Name: idx_assignments_shift_truck; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_shift_truck ON public.assignments USING btree (truck_id, shift_id) WHERE (is_shift_assignment = true);


--
-- Name: idx_assignments_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_status ON public.assignments USING btree (status);


--
-- Name: idx_assignments_status_assigned_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_status_assigned_date ON public.assignments USING btree (status, assigned_date) WHERE (status = ANY (ARRAY['assigned'::public.assignment_status, 'in_progress'::public.assignment_status]));


--
-- Name: idx_assignments_status_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_status_date ON public.assignments USING btree (status, assigned_date) WHERE (status = ANY (ARRAY['assigned'::public.assignment_status, 'in_progress'::public.assignment_status]));


--
-- Name: idx_assignments_truck; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_truck ON public.assignments USING btree (truck_id);


--
-- Name: idx_assignments_truck_driver; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_truck_driver ON public.assignments USING btree (truck_id, driver_id, assigned_date DESC);


--
-- Name: idx_assignments_truck_driver_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_truck_driver_date ON public.assignments USING btree (truck_id, driver_id, assigned_date);


--
-- Name: idx_assignments_truck_driver_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_truck_driver_status ON public.assignments USING btree (truck_id, driver_id, status);


--
-- Name: idx_assignments_truck_id_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_truck_id_status ON public.assignments USING btree (truck_id, status);


--
-- Name: idx_assignments_truck_locations; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_assignments_truck_locations ON public.assignments USING btree (truck_id, loading_location_id, unloading_location_id, assigned_date);


--
-- Name: idx_automated_fix_logs_executed_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_automated_fix_logs_executed_at ON public.automated_fix_logs USING btree (executed_at DESC);


--
-- Name: idx_automated_fix_logs_module_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_automated_fix_logs_module_name ON public.automated_fix_logs USING btree (module_name);


--
-- Name: idx_automated_fix_logs_success; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_automated_fix_logs_success ON public.automated_fix_logs USING btree (success);


--
-- Name: idx_baseline_trip; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_baseline_trip ON public.trip_logs USING btree (baseline_trip_id) WHERE (baseline_trip_id IS NOT NULL);


--
-- Name: idx_driver_shifts_active_truck_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_active_truck_date ON public.driver_shifts USING btree (truck_id, status, start_date, end_date) WHERE (status = 'active'::public.shift_status);


--
-- Name: idx_driver_shifts_date_range; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_date_range ON public.driver_shifts USING btree (start_date, end_date, recurrence_pattern);


--
-- Name: idx_driver_shifts_date_range_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_date_range_status ON public.driver_shifts USING btree (start_date, end_date, status) WHERE (status = ANY (ARRAY['active'::public.shift_status, 'scheduled'::public.shift_status]));


--
-- Name: idx_driver_shifts_datetime_combo; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_datetime_combo ON public.driver_shifts USING btree (start_date, start_time) WHERE (status = 'active'::public.shift_status);


--
-- Name: INDEX idx_driver_shifts_datetime_combo; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON INDEX public.idx_driver_shifts_datetime_combo IS 'Improves performance of date/time queries for active shifts';


--
-- Name: idx_driver_shifts_driver_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_driver_status ON public.driver_shifts USING btree (driver_id, status);


--
-- Name: INDEX idx_driver_shifts_driver_status; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON INDEX public.idx_driver_shifts_driver_status IS 'Composite index for efficient driver shift queries by driver and status';


--
-- Name: idx_driver_shifts_driver_truck_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_driver_truck_status ON public.driver_shifts USING btree (driver_id, truck_id, status);


--
-- Name: idx_driver_shifts_end_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_end_date ON public.driver_shifts USING btree (end_date);


--
-- Name: idx_driver_shifts_recurrence_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_recurrence_active ON public.driver_shifts USING btree (recurrence_pattern, status) WHERE (status = ANY (ARRAY['scheduled'::public.shift_status, 'active'::public.shift_status]));


--
-- Name: idx_driver_shifts_recurrence_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_recurrence_status ON public.driver_shifts USING btree (recurrence_pattern, status, start_date, end_date);


--
-- Name: idx_driver_shifts_security_context_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_security_context_gin ON public.driver_shifts USING gin (security_context);


--
-- Name: idx_driver_shifts_shift_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_shift_date ON public.driver_shifts USING btree (shift_date);


--
-- Name: idx_driver_shifts_start_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_start_date ON public.driver_shifts USING btree (start_date);


--
-- Name: idx_driver_shifts_start_date_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_start_date_status ON public.driver_shifts USING btree (start_date, status);


--
-- Name: idx_driver_shifts_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_status ON public.driver_shifts USING btree (status);


--
-- Name: idx_driver_shifts_status_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_status_date ON public.driver_shifts USING btree (status, start_date, end_date);


--
-- Name: idx_driver_shifts_time_range; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_time_range ON public.driver_shifts USING btree (start_time, end_time);


--
-- Name: idx_driver_shifts_truck_shift_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_truck_shift_date ON public.driver_shifts USING btree (truck_id, shift_date);


--
-- Name: idx_driver_shifts_truck_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_truck_status ON public.driver_shifts USING btree (truck_id, status) WHERE (status = ANY (ARRAY['active'::public.shift_status, 'scheduled'::public.shift_status]));


--
-- Name: idx_driver_shifts_truck_status_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_truck_status_active ON public.driver_shifts USING btree (truck_id, status) WHERE (status = 'active'::public.shift_status);


--
-- Name: idx_driver_shifts_truck_status_date_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_truck_status_date_time ON public.driver_shifts USING btree (truck_id, status, start_date, end_date, start_time, end_time);


--
-- Name: idx_driver_shifts_unified_date_range; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_unified_date_range ON public.driver_shifts USING btree (truck_id, start_date, end_date, status);


--
-- Name: idx_driver_shifts_unified_range; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_shifts_unified_range ON public.driver_shifts USING btree (truck_id, driver_id, start_date, end_date, status);


--
-- Name: idx_driver_status_audit_blocked_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_status_audit_blocked_at ON public.driver_status_audit USING btree (blocked_at DESC);


--
-- Name: idx_driver_status_audit_driver_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_status_audit_driver_id ON public.driver_status_audit USING btree (driver_id);


--
-- Name: idx_driver_status_audit_employee_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_status_audit_employee_id ON public.driver_status_audit USING btree (employee_id);


--
-- Name: idx_driver_status_audit_monitoring; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_status_audit_monitoring ON public.driver_status_audit USING btree (blocked_at DESC, driver_status, operation_attempted);


--
-- Name: idx_driver_status_audit_status_operation; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_driver_status_audit_status_operation ON public.driver_status_audit USING btree (driver_status, operation_attempted);


--
-- Name: idx_drivers_active_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_active_status ON public.drivers USING btree (status, employee_id) WHERE (status = 'active'::public.driver_status);


--
-- Name: idx_drivers_employee_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_employee_id ON public.drivers USING btree (employee_id);


--
-- Name: idx_drivers_license; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_license ON public.drivers USING btree (license_number);


--
-- Name: idx_drivers_qr_code_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_qr_code_gin ON public.drivers USING gin (driver_qr_code) WHERE (driver_qr_code IS NOT NULL);


--
-- Name: INDEX idx_drivers_qr_code_gin; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON INDEX public.idx_drivers_qr_code_gin IS 'GIN index for fast QR code lookups on drivers table';


--
-- Name: idx_drivers_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_drivers_status ON public.drivers USING btree (status);


--
-- Name: idx_health_check_logs_check_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_health_check_logs_check_name ON public.health_check_logs USING btree (check_name);


--
-- Name: idx_health_check_logs_checked_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_health_check_logs_checked_at ON public.health_check_logs USING btree (checked_at);


--
-- Name: idx_health_check_logs_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_health_check_logs_status ON public.health_check_logs USING btree (status);


--
-- Name: idx_location_sequence; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_location_sequence ON public.trip_logs USING gin (location_sequence);


--
-- Name: idx_locations_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_active ON public.locations USING btree (status) WHERE ((status)::text = 'active'::text);


--
-- Name: idx_locations_active_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_active_type ON public.locations USING btree (type, location_code) WHERE ((status)::text = 'active'::text);


--
-- Name: idx_locations_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_code ON public.locations USING btree (location_code);


--
-- Name: idx_locations_id_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_id_name ON public.locations USING btree (id, name);


--
-- Name: idx_locations_is_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_is_active ON public.locations USING btree (is_active) WHERE (is_active = true);


--
-- Name: idx_locations_qr_data_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_qr_data_gin ON public.locations USING gin (qr_code_data) WHERE (qr_code_data IS NOT NULL);


--
-- Name: idx_locations_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_locations_type ON public.locations USING btree (type);


--
-- Name: idx_mv_fleet_status_trip_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_mv_fleet_status_trip_status ON public.mv_fleet_status_summary USING btree (current_trip_status, truck_number);


--
-- Name: idx_mv_fleet_status_truck_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_mv_fleet_status_truck_id ON public.mv_fleet_status_summary USING btree (truck_id);


--
-- Name: idx_mv_trip_performance_unique; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_mv_trip_performance_unique ON public.mv_trip_performance_summary USING btree (trip_date, truck_number, COALESCE(driver_name, 'Unknown'::character varying), loading_location, unloading_location);


--
-- Name: idx_role_permissions_role; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_role_permissions_role ON public.role_permissions USING btree (role_name);


--
-- Name: idx_role_permissions_role_page; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_role_permissions_role_page ON public.role_permissions USING btree (role_name, page_key);


--
-- Name: idx_scan_logs_timestamp_user; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scan_logs_timestamp_user ON public.scan_logs USING btree (scan_timestamp DESC, scanner_user_id);


--
-- Name: INDEX idx_scan_logs_timestamp_user; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON INDEX public.idx_scan_logs_timestamp_user IS 'Optimized for scan log queries by timestamp and user';


--
-- Name: idx_scans_location; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scans_location ON public.scan_logs USING btree (scanned_location_id);


--
-- Name: idx_scans_timestamp; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scans_timestamp ON public.scan_logs USING btree (scan_timestamp);


--
-- Name: idx_scans_trip; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scans_trip ON public.scan_logs USING btree (trip_log_id);


--
-- Name: idx_scans_trip_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scans_trip_type ON public.scan_logs USING btree (trip_log_id, scan_type) WHERE (trip_log_id IS NOT NULL);


--
-- Name: idx_scans_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scans_type ON public.scan_logs USING btree (scan_type);


--
-- Name: idx_scans_user_valid_timestamp; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_scans_user_valid_timestamp ON public.scan_logs USING btree (scanner_user_id, is_valid, scan_timestamp);


--
-- Name: idx_security_logs_activity_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_security_logs_activity_type ON public.security_logs USING btree (activity_type);


--
-- Name: idx_security_logs_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_security_logs_created_at ON public.security_logs USING btree (created_at);


--
-- Name: idx_security_logs_details_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_security_logs_details_gin ON public.security_logs USING gin (details);


--
-- Name: idx_security_logs_ip_address; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_security_logs_ip_address ON public.security_logs USING btree (ip_address);


--
-- Name: idx_security_logs_risk_level; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_security_logs_risk_level ON public.security_logs USING btree (risk_level);


--
-- Name: idx_shift_handovers_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_shift_handovers_time ON public.shift_handovers USING btree (handover_time);


--
-- Name: idx_shift_handovers_trip; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_shift_handovers_trip ON public.shift_handovers USING btree (active_trip_id);


--
-- Name: idx_shift_handovers_truck; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_shift_handovers_truck ON public.shift_handovers USING btree (truck_id);


--
-- Name: idx_system_health_logs_checked_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_health_logs_checked_at ON public.system_health_logs USING btree (checked_at DESC);


--
-- Name: idx_system_health_logs_module; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_health_logs_module ON public.system_health_logs USING btree (module);


--
-- Name: idx_system_health_logs_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_health_logs_status ON public.system_health_logs USING btree (status);


--
-- Name: idx_system_logs_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_logs_created_at ON public.system_logs USING btree (created_at);


--
-- Name: idx_system_logs_log_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_logs_log_type ON public.system_logs USING btree (log_type);


--
-- Name: idx_system_logs_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_logs_user_id ON public.system_logs USING btree (user_id);


--
-- Name: idx_system_tasks_auto_executable; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tasks_auto_executable ON public.system_tasks USING btree (auto_executable) WHERE (auto_executable = true);


--
-- Name: idx_system_tasks_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tasks_created_at ON public.system_tasks USING btree (created_at DESC);


--
-- Name: idx_system_tasks_priority; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tasks_priority ON public.system_tasks USING btree (priority);


--
-- Name: idx_system_tasks_scheduled_for; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tasks_scheduled_for ON public.system_tasks USING btree (scheduled_for) WHERE (scheduled_for IS NOT NULL);


--
-- Name: idx_system_tasks_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tasks_status ON public.system_tasks USING btree (status);


--
-- Name: idx_system_tasks_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_system_tasks_type ON public.system_tasks USING btree (type);


--
-- Name: idx_trip_logs_actual_locations; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_actual_locations ON public.trip_logs USING btree (actual_loading_location_id, actual_unloading_location_id);


--
-- Name: idx_trip_logs_assignment_status_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_assignment_status_created ON public.trip_logs USING btree (assignment_id, status, created_at DESC);


--
-- Name: idx_trip_logs_assignment_status_exception; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_assignment_status_exception ON public.trip_logs USING btree (assignment_id, status, is_exception, created_at);


--
-- Name: idx_trip_logs_composite_filter; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_composite_filter ON public.trip_logs USING btree (status, is_exception, created_at DESC, assignment_id);


--
-- Name: idx_trip_logs_duration_metrics; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_duration_metrics ON public.trip_logs USING btree (total_duration_minutes, loading_duration_minutes) WHERE (total_duration_minutes IS NOT NULL);


--
-- Name: INDEX idx_trip_logs_duration_metrics; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON INDEX public.idx_trip_logs_duration_metrics IS 'Performance index for duration-based analytics';


--
-- Name: idx_trip_logs_notes_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_notes_gin ON public.trip_logs USING gin (notes) WHERE (notes IS NOT NULL);


--
-- Name: idx_trip_logs_performed_by_driver; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_performed_by_driver ON public.trip_logs USING btree (performed_by_driver_id);


--
-- Name: idx_trip_logs_performed_by_employee; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_performed_by_employee ON public.trip_logs USING btree (performed_by_employee_id);


--
-- Name: idx_trip_logs_performed_by_shift; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_performed_by_shift ON public.trip_logs USING btree (performed_by_shift_id);


--
-- Name: idx_trip_logs_search_fields; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_search_fields ON public.trip_logs USING btree (trip_number, status, created_at DESC);


--
-- Name: idx_trip_logs_stopped; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_stopped ON public.trip_logs USING btree (status) WHERE (status = 'stopped'::public.trip_status);


--
-- Name: idx_trip_logs_stopped_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_stopped_date ON public.trip_logs USING btree (stopped_reported_at) WHERE (stopped_reported_at IS NOT NULL);


--
-- Name: idx_trip_logs_stopped_reported_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_stopped_reported_at ON public.trip_logs USING btree (stopped_reported_at);


--
-- Name: idx_trip_logs_stopped_resolved_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_stopped_resolved_at ON public.trip_logs USING btree (stopped_resolved_at);


--
-- Name: idx_trip_logs_stopped_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trip_logs_stopped_status ON public.trip_logs USING btree (status) WHERE (status = 'stopped'::public.trip_status);


--
-- Name: idx_trips_actual_locations; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_actual_locations ON public.trip_logs USING btree (actual_loading_location_id, actual_unloading_location_id);


--
-- Name: idx_trips_assignment; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_assignment ON public.trip_logs USING btree (assignment_id);


--
-- Name: idx_trips_assignment_status_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_assignment_status_date ON public.trip_logs USING btree (assignment_id, status, created_at);


--
-- Name: idx_trips_assignment_status_exception; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_assignment_status_exception ON public.trip_logs USING btree (assignment_id, status, is_exception, created_at);


--
-- Name: idx_trips_current_status_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_current_status_time ON public.trip_logs USING btree (status, created_at DESC) WHERE (status = ANY (ARRAY['loading_start'::public.trip_status, 'loading_end'::public.trip_status, 'unloading_start'::public.trip_status, 'unloading_end'::public.trip_status, 'stopped'::public.trip_status]));


--
-- Name: idx_trips_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_date ON public.trip_logs USING btree (created_at);


--
-- Name: idx_trips_duration_metrics; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_duration_metrics ON public.trip_logs USING btree (total_duration_minutes, loading_duration_minutes) WHERE (total_duration_minutes IS NOT NULL);


--
-- Name: idx_trips_exception; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_exception ON public.trip_logs USING btree (is_exception);


--
-- Name: idx_trips_exception_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_exception_status ON public.trip_logs USING btree (is_exception, status) WHERE (is_exception = true);


--
-- Name: idx_trips_location_performance; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_location_performance ON public.trip_logs USING btree (actual_loading_location_id, actual_unloading_location_id, status, created_at, total_duration_minutes);


--
-- Name: idx_trips_phase_durations; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_phase_durations ON public.trip_logs USING btree (status, loading_duration_minutes, travel_duration_minutes, unloading_duration_minutes, created_at) WHERE ((status = 'trip_completed'::public.trip_status) AND (total_duration_minutes IS NOT NULL));


--
-- Name: idx_trips_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_status ON public.trip_logs USING btree (status);


--
-- Name: idx_trips_stopped_analytics; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_stopped_analytics ON public.trip_logs USING btree (status, stopped_reported_at, stopped_resolved_at, previous_status) WHERE (status = 'stopped'::public.trip_status);


--
-- Name: idx_trips_time_analytics; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trips_time_analytics ON public.trip_logs USING btree (date(created_at), EXTRACT(hour FROM created_at), status);


--
-- Name: idx_trucks_active_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trucks_active_status ON public.dump_trucks USING btree (status, truck_number) WHERE (status = 'active'::public.truck_status);


--
-- Name: idx_trucks_license; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trucks_license ON public.dump_trucks USING btree (license_plate);


--
-- Name: idx_trucks_number; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trucks_number ON public.dump_trucks USING btree (truck_number);


--
-- Name: idx_trucks_qr_data_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trucks_qr_data_gin ON public.dump_trucks USING gin (qr_code_data) WHERE (qr_code_data IS NOT NULL);


--
-- Name: idx_trucks_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_trucks_status ON public.dump_trucks USING btree (status);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_role; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_role ON public.users USING btree (role);


--
-- Name: idx_users_username; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_users_username ON public.users USING btree (username);


--
-- Name: idx_workflow_tracking; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_workflow_tracking ON public.trip_logs USING btree (workflow_type, is_extended_trip, cycle_number);


--
-- Name: assignments trg_auto_populate_driver; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trg_auto_populate_driver BEFORE INSERT ON public.assignments FOR EACH ROW EXECUTE FUNCTION public.auto_populate_driver_from_shift();


--
-- Name: trip_logs trigger_auto_capture_trip_driver; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_auto_capture_trip_driver BEFORE INSERT OR UPDATE ON public.trip_logs FOR EACH ROW EXECUTE FUNCTION public.auto_capture_trip_driver();


--
-- Name: TRIGGER trigger_auto_capture_trip_driver ON trip_logs; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TRIGGER trigger_auto_capture_trip_driver ON public.trip_logs IS 'Automatically captures active driver information when trip starts';


--
-- Name: trip_logs trigger_calculate_trip_durations; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_calculate_trip_durations BEFORE INSERT OR UPDATE ON public.trip_logs FOR EACH ROW EXECUTE FUNCTION public.calculate_trip_durations();


--
-- Name: driver_shifts trigger_set_display_type; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_set_display_type BEFORE INSERT OR UPDATE ON public.driver_shifts FOR EACH ROW EXECUTE FUNCTION public.set_display_type_trigger();


--
-- Name: driver_shifts trigger_sync_shift_date; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_sync_shift_date BEFORE INSERT OR UPDATE ON public.driver_shifts FOR EACH ROW EXECUTE FUNCTION public.sync_shift_date_with_start_date();


--
-- Name: trip_logs trigger_update_assignment_on_trip_complete; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_update_assignment_on_trip_complete AFTER UPDATE ON public.trip_logs FOR EACH ROW EXECUTE FUNCTION public.update_assignment_on_trip_complete();


--
-- Name: approvals update_approvals_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_approvals_updated_at BEFORE UPDATE ON public.approvals FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: assignments update_assignments_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_assignments_updated_at BEFORE UPDATE ON public.assignments FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: drivers update_drivers_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_drivers_updated_at BEFORE UPDATE ON public.drivers FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: locations update_locations_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_locations_updated_at BEFORE UPDATE ON public.locations FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: role_permissions update_role_permissions_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_role_permissions_updated_at BEFORE UPDATE ON public.role_permissions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: trip_logs update_trip_logs_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_trip_logs_updated_at BEFORE UPDATE ON public.trip_logs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: dump_trucks update_trucks_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_trucks_updated_at BEFORE UPDATE ON public.dump_trucks FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: approvals approvals_reported_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.approvals
    ADD CONSTRAINT approvals_reported_by_fkey FOREIGN KEY (reported_by) REFERENCES public.users(id);


--
-- Name: approvals approvals_reviewed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.approvals
    ADD CONSTRAINT approvals_reviewed_by_fkey FOREIGN KEY (reviewed_by) REFERENCES public.users(id);


--
-- Name: approvals approvals_suggested_assignment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.approvals
    ADD CONSTRAINT approvals_suggested_assignment_id_fkey FOREIGN KEY (suggested_assignment_id) REFERENCES public.assignments(id);


--
-- Name: approvals approvals_trip_log_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.approvals
    ADD CONSTRAINT approvals_trip_log_id_fkey FOREIGN KEY (trip_log_id) REFERENCES public.trip_logs(id) ON DELETE CASCADE;


--
-- Name: assignments assignments_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id) ON DELETE CASCADE;


--
-- Name: assignments assignments_loading_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_loading_location_id_fkey FOREIGN KEY (loading_location_id) REFERENCES public.locations(id) ON DELETE CASCADE;


--
-- Name: assignments assignments_shift_handover_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_shift_handover_id_fkey FOREIGN KEY (shift_handover_id) REFERENCES public.shift_handovers(id);


--
-- Name: assignments assignments_shift_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES public.driver_shifts(id);


--
-- Name: assignments assignments_truck_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_truck_id_fkey FOREIGN KEY (truck_id) REFERENCES public.dump_trucks(id) ON DELETE CASCADE;


--
-- Name: assignments assignments_unloading_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_unloading_location_id_fkey FOREIGN KEY (unloading_location_id) REFERENCES public.locations(id) ON DELETE CASCADE;


--
-- Name: driver_shifts driver_shifts_assignment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts
    ADD CONSTRAINT driver_shifts_assignment_id_fkey FOREIGN KEY (assignment_id) REFERENCES public.assignments(id);


--
-- Name: driver_shifts driver_shifts_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts
    ADD CONSTRAINT driver_shifts_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.drivers(id) ON DELETE CASCADE;


--
-- Name: driver_shifts driver_shifts_previous_shift_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts
    ADD CONSTRAINT driver_shifts_previous_shift_id_fkey FOREIGN KEY (previous_shift_id) REFERENCES public.driver_shifts(id);


--
-- Name: driver_shifts driver_shifts_truck_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_shifts
    ADD CONSTRAINT driver_shifts_truck_id_fkey FOREIGN KEY (truck_id) REFERENCES public.dump_trucks(id) ON DELETE CASCADE;


--
-- Name: trip_logs fk_baseline_trip; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT fk_baseline_trip FOREIGN KEY (baseline_trip_id) REFERENCES public.trip_logs(id) ON DELETE SET NULL;


--
-- Name: driver_status_audit fk_driver_status_audit_driver; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.driver_status_audit
    ADD CONSTRAINT fk_driver_status_audit_driver FOREIGN KEY (driver_id) REFERENCES public.drivers(id) ON DELETE CASCADE;


--
-- Name: scan_logs scan_logs_scanned_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scan_logs
    ADD CONSTRAINT scan_logs_scanned_location_id_fkey FOREIGN KEY (scanned_location_id) REFERENCES public.locations(id) ON DELETE SET NULL;


--
-- Name: scan_logs scan_logs_scanned_truck_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scan_logs
    ADD CONSTRAINT scan_logs_scanned_truck_id_fkey FOREIGN KEY (scanned_truck_id) REFERENCES public.dump_trucks(id);


--
-- Name: scan_logs scan_logs_scanner_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scan_logs
    ADD CONSTRAINT scan_logs_scanner_user_id_fkey FOREIGN KEY (scanner_user_id) REFERENCES public.users(id);


--
-- Name: scan_logs scan_logs_trip_log_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.scan_logs
    ADD CONSTRAINT scan_logs_trip_log_id_fkey FOREIGN KEY (trip_log_id) REFERENCES public.trip_logs(id) ON DELETE SET NULL;


--
-- Name: shift_handovers shift_handovers_active_trip_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_active_trip_id_fkey FOREIGN KEY (active_trip_id) REFERENCES public.trip_logs(id);


--
-- Name: shift_handovers shift_handovers_approved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_approved_by_fkey FOREIGN KEY (approved_by) REFERENCES public.users(id);


--
-- Name: shift_handovers shift_handovers_incoming_shift_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_incoming_shift_id_fkey FOREIGN KEY (incoming_shift_id) REFERENCES public.driver_shifts(id);


--
-- Name: shift_handovers shift_handovers_location_at_handover_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_location_at_handover_fkey FOREIGN KEY (location_at_handover) REFERENCES public.locations(id);


--
-- Name: shift_handovers shift_handovers_outgoing_shift_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_outgoing_shift_id_fkey FOREIGN KEY (outgoing_shift_id) REFERENCES public.driver_shifts(id);


--
-- Name: shift_handovers shift_handovers_truck_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.shift_handovers
    ADD CONSTRAINT shift_handovers_truck_id_fkey FOREIGN KEY (truck_id) REFERENCES public.dump_trucks(id) ON DELETE CASCADE;


--
-- Name: system_tasks system_tasks_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.system_tasks
    ADD CONSTRAINT system_tasks_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: trip_logs trip_logs_actual_loading_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_actual_loading_location_id_fkey FOREIGN KEY (actual_loading_location_id) REFERENCES public.locations(id);


--
-- Name: trip_logs trip_logs_actual_unloading_location_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_actual_unloading_location_id_fkey FOREIGN KEY (actual_unloading_location_id) REFERENCES public.locations(id);


--
-- Name: trip_logs trip_logs_assignment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_assignment_id_fkey FOREIGN KEY (assignment_id) REFERENCES public.assignments(id) ON DELETE CASCADE;


--
-- Name: trip_logs trip_logs_exception_approved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_exception_approved_by_fkey FOREIGN KEY (exception_approved_by) REFERENCES public.users(id);


--
-- Name: trip_logs trip_logs_performed_by_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_performed_by_driver_id_fkey FOREIGN KEY (performed_by_driver_id) REFERENCES public.drivers(id);


--
-- Name: trip_logs trip_logs_performed_by_shift_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_performed_by_shift_id_fkey FOREIGN KEY (performed_by_shift_id) REFERENCES public.driver_shifts(id);


--
-- Name: trip_logs trip_logs_stopped_resolved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.trip_logs
    ADD CONSTRAINT trip_logs_stopped_resolved_by_fkey FOREIGN KEY (stopped_resolved_by) REFERENCES public.users(id);


--
-- PostgreSQL database dump complete
--

